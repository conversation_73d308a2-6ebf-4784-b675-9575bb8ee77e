<div class="card widget-compatible" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
	id="student_attendance_day_v2_summary">
	<div class="card-header panel_heading_new_style_staff"
		style="border-top-left-radius: 8px;border-top-right-radius: 8px">
		<div class="card-title card-title-new-style">
			<div class="d-flex justify-content-between align-items-center">
				<span>Student Day Attendance V2</span>
				<div style="font-size: 12px;">
					<span style="color: #95b75d;"><i class="fa fa-circle"></i> Present: <b><?php echo isset($present) ? $present : 0; ?></b></span> |
					<span style="color: #fe970a;"><i class="fa fa-circle"></i> Absent: <b><?php echo isset($absent) ? $absent : 0; ?></b></span>
				</div>
			</div>
		</div>
	</div>
	<div class="card-body padding-1" style="padding:5px;overflow: auto;">
		<div class="row mx-0">
			<?php if (isset($attendance_type)) { ?>
				<?php if ($attendance_type == 'holiday') { ?>
					<div class="col-md-12 text-center" style="padding: 50px;">
						<i class="fa fa-calendar fa-3x text-info"></i>
						<h5 class="mt-3">Holiday</h5>
						<p class="text-muted"><?php echo $attendance_message; ?></p>
					</div>
				<?php } elseif ($attendance_type == 'event') { ?>
					<div class="col-md-12 text-center" style="padding: 50px;">
						<i class="fa fa-star fa-3x text-warning"></i>
						<h5 class="mt-3">Special Event</h5>
						<p class="text-muted"><?php echo $attendance_message; ?></p>
					</div>
				<?php } elseif ($attendance_type == 'no_sections') { ?>
					<div class="col-md-12 text-center" style="padding: 50px;">
						<i class="fa fa-info-circle fa-3x text-secondary"></i>
						<h5 class="mt-3">No Sections Assigned</h5>
						<p class="text-muted"><?php echo $attendance_message; ?></p>
					</div>
				<?php } elseif ($attendance_type == 'no_permission') { ?>
					<div class="col-md-12 text-center" style="padding: 50px;">
						<i class="fa fa-lock fa-3x text-warning"></i>
						<h5 class="mt-3">Access Denied</h5>
						<p class="text-muted"><?php echo $attendance_message; ?></p>
					</div>
				<?php } else { ?>

					<!-- Display comprehensive class/section overview -->
					<div class="col-md-12 px-0">
						<?php if (isset($class_wise_data) && !empty($class_wise_data)) { ?>
							<!-- Class-wise display for users with ADMIN permission -->
							<div style="height: 24rem; overflow-y: auto; padding: 5px;">
								<?php foreach ($class_wise_data as $class_key => $class_data) { ?>
									<div class="mb-3" style="border: 1px solid #e3e6f0; border-radius: 8px; background: #f8f9fc;">
										<!-- Class Header -->
										<div class="d-flex justify-content-between align-items-center p-2" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px 8px 0 0;">
											<div>
												<h6 class="mb-0" style="font-weight: 600;"><?php echo $class_data['class_name']; ?></h6>
												<small style="opacity: 0.9;">
													<?php echo count($class_data['sections']); ?> Section<?php echo count($class_data['sections']) > 1 ? 's' : ''; ?>
												</small>
											</div>
											<div class="text-right">
												<div style="font-size: 14px;">
													<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; margin-right: 5px;">
														<i class="fa fa-user"></i> P: <?php echo $class_data['total_present']; ?>
													</span>
													<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px;">
														<i class="fa fa-user-times"></i> A: <?php echo $class_data['total_absent']; ?>
													</span>
												</div>
											</div>
										</div>

										<!-- Sections Grid -->
										<div class="p-2">
											<div class="row">
												<?php foreach ($class_data['sections'] as $section_key => $section_data) { ?>
													<?php
													$total_students = $section_data['present'] + $section_data['absent'];
													$attendance_taken = $total_students > 0;
													$attendance_percentage = $total_students > 0 ? round(($section_data['present'] / $total_students) * 100) : 0;
													?>
													<div class="col-md-4 mb-3">
														<div class="card h-100" style="border: 2px solid <?php echo $attendance_taken ? '#28a745' : '#dc3545'; ?>; border-radius: 8px; transition: all 0.3s ease;">
															<div class="card-body p-3 text-center">
																<!-- Section Name & Status -->
																<div class="d-flex justify-content-between align-items-center mb-3">
																	<h6 class="mb-0" style="font-weight: 600; color: #2c3e50;">
																		Section <?php echo $section_data['section_name']; ?>
																	</h6>
																	<span class="badge d-flex align-items-center" style="background: <?php echo $attendance_taken ? '#28a745' : '#dc3545'; ?>; color: white; padding: 4px 8px; border-radius: 12px;">
																		<?php if ($attendance_taken) { ?>
																			<i class="fa fa-check-circle" style="font-size: 14px;" title="Attendance Taken"></i>
																		<?php } else { ?>
																			<i class="fa fa-clock-o" style="font-size: 14px;" title="Attendance Pending"></i>
																		<?php } ?>
																	</span>
																</div>

																<?php if ($attendance_taken) { ?>
																	<!-- Attendance Data - Vertical Layout -->
																	<div class="mb-3">
																		<!-- Present Count -->
																		<div class="text-center p-2 mb-2" style="background: #d4edda; border-radius: 8px;">
																			<div style="font-size: 24px; font-weight: bold; color: #155724;">
																				<?php echo $section_data['present']; ?>
																			</div>
																			<small style="color: #155724;">Present</small>
																		</div>
																		<!-- Absent Count -->
																		<div class="text-center p-2" style="background: #f8d7da; border-radius: 8px;">
																			<div style="font-size: 24px; font-weight: bold; color: #721c24;">
																				<?php echo $section_data['absent']; ?>
																			</div>
																			<small style="color: #721c24;">Absent</small>
																		</div>
																	</div>
																	<div class="mt-3">
																		<div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
																			<div style="background: linear-gradient(90deg, #28a745, #20c997); height: 100%; width: <?php echo $attendance_percentage; ?>%;"></div>
																		</div>
																		<small class="text-muted"><?php echo $attendance_percentage; ?>% Present</small>
																	</div>
																<?php } else { ?>
																	<div class="text-center" style="padding: 30px 0;">
																		<i class="fa fa-clock-o" style="font-size: 32px; color: #dc3545; margin-bottom: 10px;"></i>
																		<div style="color: #6c757d;">Attendance Pending</div>
																		<small class="text-muted">Please take attendance</small>
																	</div>
																<?php } ?>
															</div>
														</div>
													</div>
												<?php } ?>
											</div>
										</div>
									</div>
								<?php } ?>
							</div>
						<?php } else { ?>
							<!-- Section-wise display for users with only TAKE_ATTENDANCE permission -->
							<div style="height: 24rem; overflow-y: auto; padding: 10px;">
								<div class="text-center mb-3">
									<h6 style="color: #495057; font-weight: 600;">My Assigned Sections</h6>
								</div>

								<?php if (isset($attendance_data) && !empty($attendance_data)) { ?>
									<div class="row">
										<?php foreach ($attendance_data as $section) { ?>
											<?php
											$total_students = $section['present'] + $section['absent'];
											$attendance_taken = $section['attendance_taken'] && $total_students > 0;
											$attendance_percentage = $total_students > 0 ? round(($section['present'] / $total_students) * 100) : 0;
											?>
											<div class="col-md-6 mb-3">
												<div class="card" style="border: 2px solid <?php echo $attendance_taken ? '#28a745' : '#dc3545'; ?>; border-radius: 12px;">
													<div class="card-body text-center p-3">
														<div class="d-flex justify-content-between align-items-center mb-3">
															<h6 class="mb-0" style="font-weight: 600;">
																<?php echo $section['class_name']; ?> - <?php echo $section['section_name']; ?>
															</h6>
															<span class="badge" style="background: <?php echo $attendance_taken ? '#28a745' : '#dc3545'; ?>;">
																<?php echo $attendance_taken ? 'TAKEN' : 'PENDING'; ?>
															</span>
														</div>

														<?php if ($attendance_taken) { ?>
															<div class="row">
																<div class="col-6">
																	<div class="text-center p-2" style="background: #d4edda; border-radius: 8px;">
																		<div style="font-size: 24px; font-weight: bold; color: #155724;">
																			<?php echo $section['present']; ?>
																		</div>
																		<small style="color: #155724;">Present</small>
																	</div>
																</div>
																<div class="col-6">
																	<div class="text-center p-2" style="background: #f8d7da; border-radius: 8px;">
																		<div style="font-size: 24px; font-weight: bold; color: #721c24;">
																			<?php echo $section['absent']; ?>
																		</div>
																		<small style="color: #721c24;">Absent</small>
																	</div>
																</div>
															</div>
															<div class="mt-3">
																<div class="progress" style="height: 8px;">
																	<div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $attendance_percentage; ?>%;" aria-valuenow="<?php echo $attendance_percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
																</div>
																<small class="text-muted"><?php echo $attendance_percentage; ?>% Present</small>
															</div>
														<?php } else { ?>
															<div class="text-center" style="padding: 30px 0;">
																<i class="fa fa-clock-o" style="font-size: 32px; color: #dc3545; margin-bottom: 10px;"></i>
																<div style="color: #6c757d;">Attendance Pending</div>
																<small class="text-muted">Please take attendance</small>
															</div>
														<?php } ?>
													</div>
												</div>
											</div>
										<?php } ?>
									</div>
								<?php } else { ?>
									<div class="text-center" style="padding: 50px 0;">
										<i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
										<h6 class="text-muted">No sections assigned</h6>
										<small class="text-muted">Contact administrator for section assignment</small>
									</div>
								<?php } ?>
							</div>
						<?php } ?>
					</div>
				<?php } ?>
			<?php } ?>
		</div>
	</div>
</div>

<style>
.progress-circle {
	transition: all 0.3s ease;
}
.progress-circle:hover {
	transform: scale(1.1);
}

/* Widget compatibility adjustments */
#student_attendance_day_v2_summary {
	overflow: hidden;
}

#student_attendance_day_v2_summary .card-body {
	height: calc(31rem - 60px);
	overflow-y: auto;
}

/* Simple responsive adjustments */
@media (max-width: 768px) {
	#student_attendance_day_v2_summary {
		height: auto !important;
		min-height: 300px;
	}

	#student_attendance_day_v2_summary .card-body {
		height: auto;
		max-height: 400px;
	}

	#student_attendance_day_v2_summary .col-md-4 {
		flex: 0 0 100%;
		max-width: 100%;
	}
}
</style>