<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard_model extends CI_Model
{

  private $yearId;
  private $current_branch;
  public function __construct()
  {
    // Call the CI_Model constructor
    parent::__construct();
    $this->yearId =  $this->acad_year->getAcadYearId();
    $this->current_branch = $this->authorization->getCurrentBranch();
		$this->load->model('parent_ticketing_model');
  }
  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function get_staff_listCountAll()
  {
    return $this->db_readonly->select('count(*) as tStaff')
      ->from('staff_master')
      ->where('status', '1') // 1 for approved Staff
      ->get()->row();
  }

  public function getStudentData()
  {

    $this->db_readonly->select("ss.id, sd.admission_status, c.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id');
    // $this->db_readonly->from('student s');
    $this->db_readonly->where('sd.admission_status', '2'); // Approved 2
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    $this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
    $this->db_readonly->order_by('c.class_name, cs.section_name, std_name');
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_student_birthday_data() {

      $this->load->driver('cache', array('adapter' => 'file'));

      $school_sub_domain = CONFIG_ENV['school_sub_domain'];
      if (!empty($school_sub_domain)) {
          $cache_key = $school_sub_domain . "_student_birthdays";
          // $this->cache->delete($cache_key);
          if (!$result = $this->cache->get($cache_key)) {
              log_message('error', 'Creating cache ' . $cache_key);
              $details_one = $this->get_studentWiseDataforJustCelebratedBirthdayList('widget_view');
              $details_two = $this->get_studentDataforBirthdayList();
              $result = array_merge($details_one, $details_two);
              // Save the result to cache
              $half_day = 6 * 3600;
              $this->cache->save($cache_key, $result, $half_day); // Half a day cache time
          } else {
              // Cache hit, use the cached result
              log_message('error', 'cache hit! ' . $cache_key);
              $result = $this->cache->get($cache_key);
          }
      } else {
          //Do not work with cache is $school_sub_domain is not defined
          $details_one = $this->get_studentWiseDataforJustCelebratedBirthdayList('widget_view');
          $details_two = $this->get_studentDataforBirthdayList();
          $result = array_merge($details_one, $details_two);
      }

      return $result;
  }

  public function get_studentDataforBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.

    $prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as std_name";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as std_name";
    } else if ($prefix_student_name == "admission_number") {
      $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as std_name";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as std_name";
    }else {
      $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as std_name";
    }
    $currentDate = date('Y-m-d');
    $futureDate = date('Y-m-d', strtotime('+15 days'));
    $currentMonthDay = date('m-d', strtotime($currentDate));
    $futureMonthDay = date('m-d', strtotime($futureDate));

    $this->db_readonly->select("ss.id, cs.class_name, cs.section_name, $std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%b %d') as dobDisplay, CASE WHEN DATE_FORMAT(dob, '%m-%d') >= '$currentMonthDay' THEN 1 ELSE 2 END AS custom_order", false);
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id');
    // $this->db_readonly->from('student s');
    $this->db_readonly->where('not isnull(sd.dob)');
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    // $this->db_readonly->where('DATE_FORMAT(sd.dob,"%m-%d") >= DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    // $this->db_readonly->where('DATE_FORMAT(sd.dob,"%m-%d") >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 2 DAY),"%m-%d")');
    // $this->db_readonly->where('DATE_FORMAT(sd.dob,"%m-%d") BETWEEN DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 3 DAY),"%m-%d") AND DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 15 DAY),"%m-%d")');
    $this->db_readonly->where('sd.admission_status', '2'); // Approved 2
    $this->db_readonly->where('ss.promotion_status!=', '4');
    $this->db_readonly->where('ss.promotion_status!=', '5');
    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id');
    // $currentYear = date('Y', strtotime($currentDate));
    // $futureYear = date('Y', strtotime($futureDate));
    // if ($futureYear > $currentYear) {
    //   $futureDate = $currentYear . '-12-31';
    // }
    if ($futureMonthDay < $currentMonthDay) {
        // The date range crosses into the next year
        $this->db_readonly->group_start();
        $this->db_readonly->where("DATE_FORMAT(sd.dob, '%m-%d') >= '$currentMonthDay'");
        $this->db_readonly->or_where("DATE_FORMAT(sd.dob, '%m-%d') <= '$futureMonthDay'");
        $this->db_readonly->group_end();
    } else {
        // The date range is within the same year
        $this->db_readonly->where("DATE_FORMAT(sd.dob, '%m-%d') >= '$currentMonthDay'");
        $this->db_readonly->where("DATE_FORMAT(sd.dob, '%m-%d') <= '$futureMonthDay'");
    }
    $this->db_readonly->where('DATE_FORMAT(sd.dob, "%m-%d") != ', '00-00');
    $this->db_readonly->where('sd.dob != ', '1970-01-01');
    // $this->db_readonly->order_by("CASE WHEN DATE_FORMAT(sd.dob, '%m-%d') >= DATE_FORMAT(CURRENT_DATE, '%m-%d') THEN 1 ELSE 0 END, MONTH(sd.dob), DAY(sd.dob)");
    // $this->db_readonly->order_by("MONTH(sd.dob), DAY(sd.dob)");
    $this->db_readonly->order_by('custom_order ASC');
    $this->db_readonly->order_by('DATE_FORMAT(sd.dob,"%m-%d") ASC');
    $this->db_readonly->order_by("std_name ASC");
    // $this->db_readonly->limit(10);
    $result = $this->db_readonly->get()->result();
    // echo "<pre>";print_r($this->db_readonly->last_query());die();
    return $result;
  }

  public function get_studentDataforJustCelebratedBirthdayList(){
    $this->db_readonly->select("ss.id, sd.gender, c.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
    // $this->db_readonly->from('student s');
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('admission_status', '2'); // Approved 2
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") BETWEEN DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 3 DAY),"%m-%d") AND DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY),"%m-%d")');
    $this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
    $this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
    $this->db_readonly->order_by('cs.section_name');
    $this->db_readonly->order_by('sd.first_name');
    $this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_studentWiseDataforJustCelebratedBirthdayList($type){
    $this->db_readonly->select("DISTINCT DATE_FORMAT(sd.dob, '%m-%d') as dob");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id AND ss.promotion_status NOT IN (4, 5)');
    $this->db_readonly->where('sd.dob IS NOT NULL');
    $this->db_readonly->where('DATE_FORMAT(sd.dob, "%m-%d") != ', '00-00');
    $this->db_readonly->where('sd.dob != ', '1970-01-01');
    $this->db_readonly->where('sd.admission_status', '2');
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);

    $currentMonthDay = "DATE_FORMAT(CURDATE(), '%m-%d')";
    $this->db_readonly->where('DATE_FORMAT(sd.dob, "%m-%d") <', $currentMonthDay, false);
    $this->db_readonly->order_by('MONTH(sd.dob)', 'DESC');
    $this->db_readonly->order_by('DAYOFMONTH(sd.dob)', 'DESC');
    $this->db_readonly->limit(3);
    
    $distinct_dobs = $this->db_readonly->get()->result();
    if (empty($distinct_dobs)) {
      return [];
    }
    
    $dob_array = array_map(function($row) {
      return $row->dob;
    }, $distinct_dobs);
    
    if($type == 'widget_view'){
      $this->db_readonly->select("ss.id, sd.gender, c.class_name, cs.section_name, CONCAT(IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) AS std_name, DATE_FORMAT(sd.dob, '%d-%m') as dob, DATE_FORMAT(sd.dob, '%b %d') as dobDisplay");
    }else{
      $this->db_readonly->select("ss.id, sd.gender, c.class_name, cs.section_name, CONCAT(IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) AS std_name, DATE_FORMAT(sd.dob, '%d-%m') as dob, DATE_FORMAT(sd.dob, '%M %d') as dobDisplay");
    }
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id AND ss.promotion_status NOT IN (4, 5)');
    $this->db_readonly->where('sd.dob IS NOT NULL');
    $this->db_readonly->where('sd.admission_status', '2');
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    
    $this->db_readonly->where_in('DATE_FORMAT(sd.dob, "%m-%d")', $dob_array);
    
    $this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
    
    if($type == 'widget_view'){
      $this->db_readonly->order_by('MONTH(sd.dob)', 'ASC');
      $this->db_readonly->order_by('DAYOFMONTH(sd.dob)', 'ASC');
    } else {
      $this->db_readonly->order_by('MONTH(sd.dob)', 'DESC');
      $this->db_readonly->order_by('DAYOFMONTH(sd.dob)', 'DESC');
    }
    $this->db_readonly->order_by('c.class_name', 'ASC');
    $this->db_readonly->order_by('cs.section_name', 'ASC');
    $this->db_readonly->order_by('sd.first_name', 'ASC');
    
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_studentDataforTodayBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.

    $this->db_readonly->select("ss.id, sd.gender, c.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
    // $this->db_readonly->from('student s');
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('admission_status', '2'); // Approved 2
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") = DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
    $this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
    $this->db_readonly->order_by('cs.section_name');
    $this->db_readonly->order_by('sd.first_name');
    $this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_studentDataforTomorrowBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.

    $this->db_readonly->select("ss.id, c.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
    // $this->db_readonly->from('student s');
    $this->db_readonly->where('admission_status', '2'); // Approved 2
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('date_format(DATE_ADD(dob, INTERVAL -1 DAY),"%d-%m")=date_format(CURRENT_DATE,"%d-%m")');
    $this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
    $this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
    $this->db_readonly->order_by('cs.section_name');
    $this->db_readonly->order_by('sd.first_name');
    $this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
    $result = $this->db_readonly->get()->result();

    return $result;
  }

  public function getTotalStudents($sectionId = 0)
  {
    $this->db_readonly->select('count(ss.id) as stdCount');
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id');
    $this->db_readonly->where('sd.admission_status', 2);
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    $this->db_readonly->where("ss.promotion_status!='JOINED'");
    $this->db_readonly->where('ss.promotion_status!=','4');
    $this->db_readonly->where('ss.promotion_status!=','5');
    if ($sectionId)
      $this->db_readonly->where('ss.class_section_id', $sectionId);
    else
      $this->db_readonly->where('ss.class_section_id !=0');
    return $this->db_readonly->get()->row()->stdCount;

    // $this->db_readonly->select('count(id) as stdCount');
    // $this->db_readonly->where('admission_status', 2);
    // if($sectionId)
    //   $this->db_readonly->where('class_section_id', $sectionId);
    // else 
    //   $this->db_readonly->where('class_section_id !=0');
    // return $this->db_readonly->get('student')->row()->stdCount;
    // return $this->db_readonly->query('select count(id) as stdCount from student where admission_status=2')->row()->stdCount;
  }

  public function get_studentDataforUpBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.

    $this->db_readonly->select("ss.id, c.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
    // $this->db_readonly->from('student s');
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('DATE_FORMAT(sd.dob, "%m") = DATE_FORMAT(CURRENT_DATE, "%m")');
    $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") > DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->where('admission_status', '2'); // Approved 2
    $this->db_readonly->where('ss.acad_year_id', $this->yearId);
    $this->db_readonly->where('date_format(DATE_ADD(dob, INTERVAL -1 DAY),"%d-%m")!=date_format(CURRENT_DATE,"%d-%m")');
    $this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
    $this->db_readonly->order_by('MONTH(dob),DAYOFMONTH(dob)');
    $this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
    $this->db_readonly->order_by('cs.section_name');
    $this->db_readonly->order_by('sd.first_name');
    $this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
    // $this->db_readonly->limit(50);
    $result = $this->db_readonly->get()->result();

    return $result;
  }

  public function getCurrentDayEvents(){
    $currentDate = date('Y-m-d');
    $this->db_readonly->select("
      id, 
      task_name, 
      DATE_FORMAT(due_date, '%d-%m') as task_due_date, 
      DATE_FORMAT(due_date, '%b %d') as dueDateDisplay, 
      task_type", 
      false
    );
    $this->db_readonly->from('daily_planner');
    $this->db_readonly->where('created_by', $this->authorization->getAvatarStakeHolderId());
    $this->db_readonly->where('status', 1);
    $this->db_readonly->where('is_deleted', 0);
    $this->db_readonly->where('not isnull(due_date)');
    $this->db_readonly->where('DATE(due_date)', $currentDate);
    $this->db_readonly->where("DATE_FORMAT(due_date, '%m-%d') != ", '00-00');
    $this->db_readonly->where('due_date != ', '1970-01-01');
    $this->db_readonly->order_by("DATE_FORMAT(due_date, '%m-%d') ASC");
    $result = $this->db_readonly->get()->result();
    if (!empty($result)) {
      foreach ($result as $task => $val) {
        if (!empty($val->task_type)) {
          $val->task_type = json_decode($val->task_type);
        }
      }
      foreach ($result as $task) {
        $task->task_type = json_decode($task->task_type);

        if (!empty($task->task_type)) {
          $this->db_readonly->select('id, task_type_color');
          $this->db_readonly->where_in('id', $task->task_type);
          $task_types = $this->db_readonly->get('stb_tasktypes')->row();
          if (!empty($task_types)) {
              $task->task_type_colors = $task_types->task_type_color;
          } else {
              $task->task_type_colors = '#CCCCCC';
          }
        } else {
          $task->task_type_colors = '#CCCCCC';
        }
      }
    }
    return $result;
  }

  public function getDailyEventsData(){
    $currentDate = date('Y-m-d', strtotime('-2 days'));
    $futureDate = date('Y-m-d', strtotime('+15 days'));
    $currentMonthDay = date('m-d', strtotime($currentDate));
    $futureMonthDay = date('m-d', strtotime($futureDate));

    $this->db_readonly->select("
        id, 
        task_name, 
        DATE_FORMAT(due_date, '%d-%m') as task_due_date, 
        DATE_FORMAT(due_date, '%b %d') as dueDateDisplay, 
        CASE 
            WHEN DATE_FORMAT(due_date, '%m-%d') >= '$currentMonthDay' THEN 1 
            ELSE 2 
        END AS custom_order, task_type", 
        false
    );
    $this->db_readonly->from('daily_planner');
    $this->db_readonly->where('created_by', $this->authorization->getAvatarStakeHolderId());
    $this->db_readonly->where('status', 1);
    $this->db_readonly->where('is_deleted', 0);
    $this->db_readonly->where('not isnull(due_date)');
    if ($futureMonthDay < $currentMonthDay) {
        $this->db_readonly->group_start();
        $this->db_readonly->where("DATE_FORMAT(due_date, '%m-%d') >= '$currentMonthDay'");
        $this->db_readonly->or_where("DATE_FORMAT(due_date, '%m-%d') <= '$futureMonthDay'");
        $this->db_readonly->group_end();
    } else {
        $this->db_readonly->where("DATE_FORMAT(due_date, '%m-%d') >= '$currentMonthDay'");
        $this->db_readonly->where("DATE_FORMAT(due_date, '%m-%d') <= '$futureMonthDay'");
    }
    $this->db_readonly->where("DATE_FORMAT(due_date, '%m-%d') != ", '00-00');
    $this->db_readonly->where('due_date != ', '1970-01-01');
    $this->db_readonly->order_by('custom_order ASC');
    $this->db_readonly->order_by("DATE_FORMAT(due_date, '%m-%d') ASC");
    $result = $this->db_readonly->get()->result();
    if(!empty($result)){
			foreach ($result as $task => $val) {
				if(!empty($val->task_type)){
					$val->task_type = json_decode($val->task_type);
				}
			}
			foreach ($result as $task) {
				$task->task_type = json_decode($task->task_type);
				
				if (!empty($task->task_type)) {
					$this->db_readonly->select('id, task_type_color');
					$this->db_readonly->where_in('id', $task->task_type);
					$task_types = $this->db_readonly->get('stb_tasktypes')->row();
					if(!empty($task_types)){
						$task->task_type_colors = $task_types->task_type_color;
					} else {
						$task->task_type_colors = '#CCCCCC';
					}
				} else {
					$task->task_type_colors = '#CCCCCC';
				}
			}
		}
    // echo "<pre>";print_r($result);die();
    return $result;
  }

  public function get_staff_birthday_data() {
      $this->load->driver('cache', array('adapter' => 'file'));

      $school_sub_domain = CONFIG_ENV['school_sub_domain'];
      if (!empty($school_sub_domain)) {
          $cache_key = $school_sub_domain . "_staff_birthdays";
          // $this->cache->delete($cache_key);
          // Check if the result is already cached
          if (!$result = $this->cache->get($cache_key)) {
              // Cache miss, so query the database
              log_message('error', 'Creating cache ' . $cache_key);
              $details_one = $this->get_staffWiseJustCelebratedBirthdayList('widget_view');
              $details_two = $this->get_staffDataforTodayBirthdayList('widget_view');
              $result = array_merge($details_one, $details_two);
              // Save the result to cache
              $half_day = 6 * 3600;
              $this->cache->save($cache_key, $result, $half_day); // Half a day cache time
          } else {
              // Cache hit, use the cached result
              log_message('error', 'cache hit! ' . $cache_key);
              $result = $this->cache->get($cache_key);
          }
      } else {
          //Do not work with cache is $school_sub_domain is not defined
          $details_one = $this->get_staffWiseJustCelebratedBirthdayList('widget_view');
          $details_two = $this->get_staffDataforTodayBirthdayList('widget_view');
          $result = array_merge($details_one, $details_two);
      }

      return $result;
  }

  public function get_staffDataforTodayBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.
    $currentDate = date('Y-m-d');
    $futureDate = date('Y-m-d', strtotime('+15 days'));
    $currentMonthDay = date('m-d', strtotime($currentDate));
    $futureMonthDay = date('m-d', strtotime($futureDate));

    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(dob,'%d-%m') as dob, date_format(dob, '%b %d') as dobDisplay, CASE WHEN DATE_FORMAT(dob, '%m-%d') >= '$currentMonthDay' THEN 1 ELSE 2 END AS custom_order", false);
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('not isnull(dob)');
    // $currentYear = date('Y', strtotime($currentDate));
    // $futureYear = date('Y', strtotime($futureDate));
    // if ($futureYear > $currentYear) {
    //   $futureDate = $currentYear . '-12-31';
    // }
    if ($futureMonthDay < $currentMonthDay) {
        // The date range crosses into the next year
        $this->db_readonly->group_start();
        $this->db_readonly->where("DATE_FORMAT(dob, '%m-%d') >= '$currentMonthDay'");
        $this->db_readonly->or_where("DATE_FORMAT(dob, '%m-%d') <= '$futureMonthDay'");
        $this->db_readonly->group_end();
    } else {
        // The date range is within the same year
        $this->db_readonly->where("DATE_FORMAT(dob, '%m-%d') >= '$currentMonthDay'");
        $this->db_readonly->where("DATE_FORMAT(dob, '%m-%d') <= '$futureMonthDay'");
    }
    $this->db_readonly->where('DATE_FORMAT(dob, "%m-%d") != ', '00-00');
    $this->db_readonly->where('dob != ', '1970-01-01');
    // $this->db_readonly->where("DATE_FORMAT(dob, '%m-%d') >= DATE_FORMAT('$currentDate', '%m-%d')");
    // $this->db_readonly->where("DATE_FORMAT(dob, '%m-%d') <= DATE_FORMAT('$futureDate', '%m-%d')");
    // $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") >= DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    // $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 2 DAY),"%m-%d")');
    // $this->db_readonly->where("(DATE_FORMAT(dob, '%m-%d') >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 3 DAY), '%m-%d') AND DATE_FORMAT(dob, '%m-%d') <= DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 15 DAY), '%m-%d'))");
    // $this->db_readonly->order_by("CASE WHEN DATE_FORMAT(dob, '%m-%d') >= DATE_FORMAT(CURRENT_DATE, '%m-%d') THEN 1 ELSE 0 END, MONTH(dob), DAYOFMONTH(dob)");
    $this->db_readonly->order_by('custom_order ASC');
    $this->db_readonly->order_by('DATE_FORMAT(dob,"%m-%d") ASC');
    $this->db_readonly->order_by('first_name ASC');
    // $this->db_readonly->order_by('MONTH(dob),DAYOFMONTH(dob)');
    // $this->db_readonly->order_by("first_name");
    // $this->db_readonly->order_by("date_format(dob, '%b %d')");
    // $this->db_readonly->limit(10);
    $result = $this->db_readonly->get()->result();
    //  echo "<pre>"; print_r($result); die();
    return $result;
  }

  public function get_staffWiseJustCelebratedBirthdayList($type){
    $this->db_readonly->select("DISTINCT DATE_FORMAT(dob, '%m-%d') as dob");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); // Approved
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('dob IS NOT NULL');
    $this->db_readonly->where('DATE_FORMAT(dob, "%m-%d") != ', '00-00');
    $this->db_readonly->where('dob != ', '1970-01-01');
    $currentMonthDay = "DATE_FORMAT(CURDATE(), '%m-%d')";
    $this->db_readonly->where('DATE_FORMAT(dob, "%m-%d") <', $currentMonthDay, false);
    $this->db_readonly->order_by('MONTH(dob)', 'DESC');
    $this->db_readonly->order_by('DAYOFMONTH(dob)', 'DESC');
    $this->db_readonly->limit(3);
    $distinct_dobs = $this->db_readonly->get()->result();
    if (empty($distinct_dobs)) {
      return [];
    }
    
    $dob_array = array_map(function($row) {
      return $row->dob;
    }, $distinct_dobs);
    
    if($type == 'widget_view'){
      $this->db_readonly->select("id, gender, CONCAT(IFNULL(first_name,''), ' ', IFNULL(last_name,'')) AS staff_name, DATE_FORMAT(dob,'%d-%m') as dob, DATE_FORMAT(dob, '%b %d') as dobDisplay");
    }else{
      $this->db_readonly->select("id, gender, CONCAT(IFNULL(first_name,''), ' ', IFNULL(last_name,'')) AS staff_name, DATE_FORMAT(dob,'%d-%m') as dob, DATE_FORMAT(dob, '%M %d') as dobDisplay");
    }
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); // Approved
    $this->db_readonly->where('dob IS NOT NULL');
    $this->db_readonly->where('is_primary_instance', 1);
    
    $this->db_readonly->where_in('DATE_FORMAT(dob, "%m-%d")', $dob_array);
    $this->db_readonly->order_by('MONTH(dob)', 'ASC');
    $this->db_readonly->order_by('DAYOFMONTH(dob)', 'ASC');
    $this->db_readonly->order_by('first_name');
    
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_staffJustCelebratedBirthdayList(){
    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(dob,'%d-%m') as dob, date_format(dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") BETWEEN DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 3 DAY),"%m-%d") AND DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY),"%m-%d")');
    $this->db_readonly->order_by('MONTH(dob),DAYOFMONTH(dob)');
    $this->db_readonly->order_by('DATE_FORMAT(dob,"%m-%d")');
    $this->db_readonly->order_by("first_name");
    // $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->result();
     // echo "<pre>"; print_r($result); die();
    return $result;
  }

  public function get_staffTodayBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.

    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(dob,'%d-%m') as dob, date_format(dob, '%b %d') as dobDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") = DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->order_by('MONTH(dob),DAYOFMONTH(dob)');
    $this->db_readonly->order_by('DATE_FORMAT(dob,"%m-%d")');
    $this->db_readonly->order_by("first_name");
    $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->result();
     // echo "<pre>"; print_r($result); die();
    return $result;
  }

  public function get_staffDataforTomorrowBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.

    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(dob,'%d-%m') as dob, date_format(dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('date_format(DATE_ADD(dob, INTERVAL -1 DAY),"%d-%m")=date_format(CURRENT_DATE,"%d-%m")');
    $this->db_readonly->order_by('DATE_FORMAT(dob,"%m-%d")');
    $this->db_readonly->order_by("first_name");
    $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($result); die();
    return $result;
  }

  public function get_staffDataforUpBirthdayList()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.

    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(dob,'%d-%m') as dob, date_format(dob, '%M %d') as dobDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('not isnull(dob)');
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('DATE_FORMAT(dob, "%m") = DATE_FORMAT(CURRENT_DATE, "%m")');
    $this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") > DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->where('date_format(DATE_ADD(dob, INTERVAL -1 DAY),"%d-%m")!=date_format(CURRENT_DATE,"%d-%m")');
    $this->db_readonly->order_by('DATE_FORMAT(dob,"%m-%d")');
    $this->db_readonly->order_by("first_name");
    // $this->db_readonly->limit(50);
    $result = $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
    return $result;
  }

  public function get_todaysEvents()
  {
    $today = date("Y-m-d");
    $sql = "SELECT `event_name`,`from_date`,`event_type` FROM `school_calender` WHERE (`from_date`='$today' OR `to_date`='$today' OR (`from_date`>='$today' AND `to_date`<='$today')) AND (applicable_to=1 OR applicable_to=3)";
    $query = $this->db_readonly->query($sql);
    return $query->result();
  }

  public function get_upcomingEvents()
  {
    $today = date("Y-m-d");
    $sql = "SELECT `event_name`,`from_date`,`to_date`,`event_type` FROM `school_calender` WHERE (`from_date`>'$today' OR `to_date`>'$today') AND (applicable_to=1 OR applicable_to=3) ORDER BY `from_date`,`to_date` LIMIT 5";
    $query = $this->db_readonly->query($sql);
    return $query->result();
  }

  public function getAllEvents()
  {
    $today = date('Y-m-d');
    $sql = "SELECT `event_name`,`from_date`,`to_date`,`event_type` FROM `school_calender` WHERE ((`from_date`>='$today') AND (`to_date`>='$today' OR `to_date` IS NULL)) AND (applicable_to=1 OR applicable_to=3) ORDER BY `from_date`,`to_date` LIMIT 5";
    $query = $this->db_readonly->query($sql);
    return $query->result();
  }

  //get publications
  public function getAllPublications($stdId, $section)
  {
    $this->db_readonly->select('pm.id,pm.published_date,pm.content,pm.category');
    $this->db_readonly->from('publication_master pm');
    $this->db_readonly->join('published_to pt', 'pt.publication_id=pm.id');
    $this->db_readonly->where('pt.student_or_section_id', $stdId);
    $this->db_readonly->where('pm.user_type', 'Student');
    $this->db_readonly->or_where('pt.student_or_section_id', $section);
    $this->db_readonly->where('pm.user_type', 'Class');
    $this->db_readonly->order_by('pm.published_date', 'desc');
    $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function getStaffLeaves()
  {
    $today = date('Y-m-d');
    $this->db_readonly->select("ls.id, ls.staff_id,ls.status,ls.from_date, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS staffName");
    $this->db_readonly->from('leave_staff ls');
    $this->db_readonly->join('staff_master s', 's.id=ls.staff_id');
    $this->db_readonly->where("(ls.from_date='$today' OR ls.to_date='$today' OR (ls.from_date<'$today' AND ls.to_date>'$today')) AND (ls.status=1 OR ls.status=2)");
    return $this->db_readonly->get()->result();
  }

  public function getStaffLeavenew_ByDate(){
    $isMultiLevelLeaveEnabled=(int)$this->settings->getSetting("enable_multi_level_leave_approver_mode");
    $today = date('Y-m-d');
    
    $this->db_readonly->select("ls.id, ls.staff_id,ls.status,ls.from_date, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS staffName, lvc.short_name as lc_name")
      ->from('leave_v2_staff ls')
      ->join('leave_v2_category lvc', 'ls.leave_category_id=lvc.id')
      ->join('staff_master sm', 'sm.id=ls.staff_id and sm.is_primary_instance=1')
      ->where('sm.status', '2');
      if($isMultiLevelLeaveEnabled){
        $this->db_readonly->where("(ls.from_date<='$today' AND ls.to_date>='$today') AND (ls.final_status=0 OR ls.final_status=1 OR ls.final_status=2)");
      }else{
        $this->db_readonly->where("(ls.from_date<='$today' AND ls.to_date>='$today') AND (ls.status=0 OR ls.status=1 OR ls.status=2)");
      }
      return $this->db_readonly->get()->result();
  }

  public function get_staff_attendance_checkin_data() {
    $today = date('Y-m-d');
    // Make checked_in staff list
    $checked_in_staffs = $this->db_readonly->select("sm.id")
      ->from('staff_master sm')
      ->join('st_attendance sta', "sta.staff_id=sm.id and sta.date='$today'")
      ->where('sm.status', '2')
      ->where('sm.is_primary_instance', 1)
      ->get()->result();

    $checked_in_staff_list = [];
    if (!empty($checked_in_staffs)) {
      foreach ($checked_in_staffs as $key => $val) {
        $checked_in_staff_list[] = $val->id;
      }
    }

    $staff_count_obj = $this->db_readonly->select('count(*) as staff_count')
      ->from('staff_master sm')
      ->join("st_attendance_staff_shifts ss", "ss.staff_id=sm.id and ss.date='$today'")
      ->where('sm.status', '2')
      ->where('sm.is_primary_instance', 1)
      ->get()->row();

    $checkin_count = count($checked_in_staff_list);

    $is_three_level_approve_enabled = (int) $this->settings->getSetting("enable_multi_level_leave_approver_mode");

    $total_staffs_on_leave_count=0;
    $total_staffs_on_leave=[];
    if($is_three_level_approve_enabled){
      $this->db_readonly->select("lvs.staff_id as staff_id")
        ->from("leave_v2_staff lvs")
        ->join("staff_master sm", "sm.id=lvs.staff_id")
        ->where("lvs.from_date<=", "$today")
        ->where("lvs.to_date>=", "$today")
        ->where('sm.status', '2')
        ->where('lvs.final_status!=', '3')
        ->where('lvs.final_status!=', '4')
        ->where('sm.is_primary_instance', 1);

        if(!empty($checked_in_staff_list)){
          $this->db_readonly->where_not_in("lvs.staff_id",$checked_in_staff_list);
        }

        $this->db_readonly->group_by("sm.id");
        $total_staffs_on_leave = $this->db_readonly->get()->result();
    }else{
      $this->db_readonly->select("lvs.staff_id as staff_id")
        ->from("leave_v2_staff lvs")
        ->join("staff_master sm", "sm.id=lvs.staff_id")
        ->where("lvs.from_date<=", "$today")
        ->where("lvs.to_date>=", "$today")
        ->where('sm.status', '2')
        ->where('lvs.status!=', '3')
        ->where('lvs.status!=', '4')
        ->where('sm.is_primary_instance', 1);

        if(!empty($checked_in_staff_list)){
          $this->db_readonly->where_not_in("lvs.staff_id",$checked_in_staff_list);
        }

        $this->db_readonly->group_by("sm.id");

        $total_staffs_on_leave = $this->db_readonly->get()->result();
    }

    $total_staffs_on_leave_count=count($total_staffs_on_leave);

    // Make leave applied staff list
    $on_leave_staff_list = [];
    if(!empty($total_staffs_on_leave)){
      foreach($total_staffs_on_leave as $key => $val){
        $on_leave_staff_list[]=$val->staff_id;
      }
    }

    // getting staffs on week off
    $staff_on_week_off=$this->db_readonly->select("ifnull(count(ss.id),0) as total_staff_on_week_off")
      ->from("st_attendance_staff_shifts ss")
      ->join('staff_master sm',"sm.id=ss.staff_id")
      ->where('sm.status', '2')
      ->where('sm.is_primary_instance', 1)
      ->where("ss.date", "$today")
      ->where("ss.type",2); // for Week off

      if(!empty($checked_in_staff_list)){
        $this->db_readonly->where_not_in("sm.id",$checked_in_staff_list);
      }

      if(!empty($on_leave_staff_list)){
        $this->db_readonly->where_not_in("sm.id", $on_leave_staff_list);
      }

    $staff_on_week_off=$this->db_readonly->get()->row();

    // getting staffs on holiday
    $this->db_readonly->select("ifnull(count(ss.id),0) as total_staff_on_holiday")
      ->from("st_attendance_staff_shifts ss")
      ->join('staff_master sm', "sm.id=ss.staff_id")
      ->where('sm.status', '2')
      ->where('sm.is_primary_instance', 1)
      ->where("ss.date", "$today")
      ->where("ss.type", 3); // for Holiday

      if(!empty($checked_in_staff_list)){
        $this->db_readonly->where_not_in("sm.id", $checked_in_staff_list);
      }

      if(!empty($on_leave_staff_list)){
        $this->db_readonly->where_not_in("sm.id", $on_leave_staff_list);
      }

    $staff_on_holiday = $this->db_readonly->get()->row();

    $obj = new stdClass();
    $obj->staff_count = $staff_count_obj->staff_count;
    $obj->checkin_count = $checkin_count;
    $obj->total_staffs_on_leave_count = $total_staffs_on_leave_count;
    $obj->total_staff_on_week_off = $staff_on_week_off->total_staff_on_week_off;
    $obj->total_staff_on_holiday=$staff_on_holiday->total_staff_on_holiday;
    $obj->not_checkin_count = $staff_count_obj->staff_count - ($checkin_count + $obj->total_staffs_on_leave_count + $obj->total_staff_on_week_off + $obj->total_staff_on_holiday);

    return $obj;
  }

  public function get_staffIdbyAvatarId($avatarId)
  {
    return $this->db_readonly->select('stakeholder_id as staffId')
      ->from('avatar')
      ->where('id', $avatarId)
      ->get()->row();
  }

  public function getAttendanceStatus()
  {

    $result1 = $this->db_readonly->select("CONCAT(ifnull(c.id,''), '_', ifnull(cs.id,'')) AS class_section, c.id as cid,cs.id as csid,c.class_name as cname,cs.section_name as csname")
      ->from('class as c')
      ->join('class_section as cs', 'cs.class_id = c.id')
      ->where('c.acad_year_id', $this->yearId)
      ->where('c.is_placeholder', '0')
      ->where('cs.is_placeholder', '0')
      ->get()->result();

    $result2 = $this->db_readonly->select("CONCAT(ifnull(class_id,''), '_', ifnull(class_section_id,'')) AS class_section")
      ->from('attendance_session')
      ->where('day', date('Y-m-d'))
      ->get()->result();

    foreach ($result1 as $key => $value) {
      foreach ($result2 as $key2 => $value2) {
        if ($value2->class_section == $value->class_section)
          $result1[$key]->attendanceTaken = 'yes';
      }
    }

    $result = [];

    foreach ($result1 as $key3 => $value3) {
      $result[$value3->cid][] = $value3;
    }

    $max_section = $this->db_readonly->query("select max(id) as max_val from (SELECT count(class_id) as id FROM `class_section` GROUP BY class_id) as t1")->result();

    return ['data' => array_values($result), 'max_section' => $max_section[0]->max_val];
  }

  public function isClassTeacher($staffId)
  {
    $result = $this->db_readonly->query('select id from class_section where class_teacher_id=' . $staffId)->row();
    if (empty($result))
      return -1;
    else
      return $result->id;
  }


  public function getAttendanceStudentsStatus($sectionId = 0)
  {

    $this->db_readonly->select("id");
    $this->db_readonly->from('attendance_session');
    $this->db_readonly->where('day', date('Y-m-d'));
    if ($sectionId)
      $this->db_readonly->where('class_section_id', $sectionId);
    $result =  $this->db_readonly->get()->result_array();

    if (!empty($result)) {

      $all_ids = [];


      foreach ($result as $key => $value) {
        $all_ids[] = $value['id'];
      }

      $max_section = $this->db_readonly->query('Select as1_status as type ,count(as1_status) as att_count
        FROM (SELECT as1.student_id, IF(sum(as1.`status`) = 0, "A", "P") as as1_status
        FROM `attendance_student` AS as1 
        WHERE as1.`attendance_session_id` in (' . implode(',', $all_ids) . ')
        GROUP BY as1.`student_id`) AS asMain
        GROUP BY as1_status')->result_array();

      // echo '<pre>'; print_r($max_section); die();

      return $max_section;
    } else {
      $t[] = ['type' => 'A', 'att_count' => 0];
      $t[] = ['type' => 'P', 'att_count' => 0];
      return $t;
    }
  }
  public function getAttendancev2StudentsStatus($sectionId = 0)
  {
    $staffId = $this->authorization->getAvatarStakeHolderId();
    $today = date('Y-m-d');

    // Check permissions to determine which sections to include
    if ($this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2_ADMIN')) {
      // Admin can view all sections
      if ($sectionId == 0) {
        $sections = $this->db_readonly
          ->select('cs.id as sectionID')
          ->from('class c')
          ->join('class_section cs', 'cs.class_id=c.id')
          ->where('c.acad_year_id', $this->acad_year->getAcadYearId())
          ->get()->result();

        if (empty($sections)) {
          return [
            'type' => 'no_sections',
            'message' => 'No sections found in the system',
            'data' => [
              ['type' => 'A', 'att_count' => 0],
              ['type' => 'P', 'att_count' => 0]
            ]
          ];
        }

        $sectionIds = array_column($sections, 'sectionID');
      } else {
        $sectionIds = [$sectionId];
      }
    } else if ($this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.TAKE_ATTENDANCE')) {
      // Staff can only view their assigned sections
      if ($sectionId == 0) {
        $sections = $this->db_readonly
          ->select('cs.id as sectionID')
          ->from('class c')
          ->join('class_section cs', 'cs.class_id=c.id')
          ->where('c.acad_year_id', $this->acad_year->getAcadYearId())
          ->group_start()
            ->where('cs.class_teacher_id', $staffId)
            ->or_where('cs.assistant_class_teacher_id', $staffId)
            ->or_where('cs.assistant_class_teacher_2', $staffId)
          ->group_end()
          ->get()->result();

        if (empty($sections)) {
          return [
            'type' => 'no_sections',
            'message' => 'No sections assigned to this staff member',
            'data' => [
              ['type' => 'A', 'att_count' => 0],
              ['type' => 'P', 'att_count' => 0]
            ]
          ];
        }

        $sectionIds = array_column($sections, 'sectionID');
      } else {
        // Verify the staff has permission for this specific section
        $hasPermission = $this->db_readonly
          ->select('cs.id')
          ->from('class c')
          ->join('class_section cs', 'cs.class_id=c.id')
          ->where('cs.id', $sectionId)
          ->where('c.acad_year_id', $this->acad_year->getAcadYearId())
          ->group_start()
            ->where('cs.class_teacher_id', $staffId)
            ->or_where('cs.assistant_class_teacher_id', $staffId)
            ->or_where('cs.assistant_class_teacher_2', $staffId)
          ->group_end()
          ->get()->row();

        if (!$hasPermission) {
          return [
            'type' => 'no_permission',
            'message' => 'You do not have permission to view this section',
            'data' => [
              ['type' => 'A', 'att_count' => 0],
              ['type' => 'P', 'att_count' => 0]
            ]
          ];
        }

        $sectionIds = [$sectionId];
      }
    } else {
      // Staff has no attendance permissions
      return [
        'type' => 'no_permission',
        'message' => 'You do not have permission to view attendance',
        'data' => [
          ['type' => 'A', 'att_count' => 0],
          ['type' => 'P', 'att_count' => 0]
        ]
      ];
    }

    // Check if today is a holiday for any of the sections
    $isHoliday = $this->_checkHolidayForSections($today, $sectionIds);
    if ($isHoliday) {
      return [
        'type' => 'holiday',
        'message' => 'Today is a holiday',
        'data' => [
          ['type' => 'H', 'att_count' => 'Holiday'],
          ['type' => 'P', 'att_count' => 0]
        ]
      ];
    }

    // Check for special events
    $events = $this->_checkEventsForSections($today, $sectionIds);
    if (!empty($events)) {
      $eventNames = array_map(function($event) { return $event->event_name; }, $events);
      $eventMessage = count($events) > 1
        ? 'Multiple events today: ' . implode(', ', array_slice($eventNames, 0, 2)) . (count($events) > 2 ? '...' : '')
        : 'Special event today: ' . $events[0]->event_name;

      return [
        'type' => 'event',
        'message' => $eventMessage,
        'events' => $events,
        'data' => [
          ['type' => 'E', 'att_count' => 'Event'],
          ['type' => 'P', 'att_count' => 0]
        ]
      ];
    }

    // Check if user has admin permission to view all sections (class-wise display)
    if ($this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2_ADMIN')) {
      return $this->_getClassWiseAttendanceData($today, $sectionIds);
    } else {
      return $this->_getSectionWiseAttendanceData($today, $sectionIds);
    }
  }

  private function _checkHolidayForSections($date, $sectionIds)
  {
    $result = $this->db_readonly->select('ce.id')
      ->from('calendar_events_v2 ce')
      ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = ce.calendar_v2_master_id')
      ->where_in('cea.assigned_section_id', $sectionIds)
      ->where('ce.event_type IN ("holiday", "holiday_range")')
      ->where('ce.from_date <=', $date)
      ->where('ce.to_date >=', $date)
      ->get()
      ->row();
    return !empty($result);
  }

  private function _checkEventsForSections($date, $sectionIds)
  {
    // Check for calendar events from calendar_events_v2 system (non-holiday events)
    $calendarEvents = $this->db_readonly->select('ce.event_name, ce.event_type')
      ->from('calendar_events_v2 ce')
      ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = ce.calendar_v2_master_id')
      ->where_in('cea.assigned_section_id', $sectionIds)
      ->where('cea.assigned_type', 'SEC')
      ->where('ce.from_date <=', $date)
      ->where('ce.to_date >=', $date)
      ->where_not_in('ce.event_type', ['holiday', 'holiday_range']) // Exclude holidays
      ->get()
      ->result();

    // Prepare events array
    $allEvents = [];

    // Add calendar events
    foreach ($calendarEvents as $event) {
      $allEvents[] = (object)[
        'event_name' => $event->event_name,
        'event_type' => $event->event_type,
        'source' => 'calendar_v2'
      ];
    }

    // Note: attendance_std_v2_calendarevents table check removed as table doesn't exist
    // If this table is created in the future, uncomment the following code:
    /*
    // Check if attendance_std_v2_calendarevents table exists
    if ($this->db->table_exists('attendance_std_v2_calendarevents')) {
      $attendanceEvents = $this->db_readonly->select('ace.event_name')
        ->from('attendance_std_v2_calendarevents ace')
        ->where('ace.date', $date)
        ->where_in('ace.section', $sectionIds)
        ->get()
        ->result();

      // Add attendance events
      foreach ($attendanceEvents as $event) {
        $allEvents[] = (object)[
          'event_name' => $event->event_name,
          'event_type' => 'attendance_event',
          'source' => 'attendance_v2'
        ];
      }
    }
    */

    return $allEvents;
  }

  private function _getClassWiseAttendanceData($today, $sectionIds)
  {
    // Get ALL sections that the user has access to (whether attendance taken or not)
    $all_sections = $this->db_readonly->select("cs.id as class_section_id, c.id as class_id, c.class_name, cs.section_name")
      ->from('class_section cs')
      ->join('class c', 'c.id = cs.class_id')
      ->where_in('cs.id', $sectionIds)
      ->order_by('c.class_name, cs.section_name')
      ->get()->result_array();

    // Get attendance sessions for today
    $sessions = $this->db_readonly->select("ads.id, ads.class_section_id")
      ->from('attendance_std_day_v2_session ads')
      ->where('ads.att_taken_date', $today)
      ->where_in('ads.class_section_id', $sectionIds)
      ->get()->result_array();

    $session_ids = array_column($sessions, 'id');
    $sections_with_attendance = array_column($sessions, 'class_section_id');

    // Get attendance data for sections where attendance was taken
    $attendance_data = [];
    if (!empty($session_ids)) {
      $attendance_data = $this->db_readonly->query('SELECT ads.class_section_id, c.id as class_id, c.class_name, cs.section_name,
                                                           as1_status as type, count(as1_status) as att_count
        FROM (SELECT asds.student_admission_id, asds.attendance_day_v2_session_id,
                     IF((asds.morning_session_status + asds.afternoon_session_status) = 0, "A", "P") as as1_status
              FROM `attendance_std_day_v2_students` AS asds
              WHERE asds.`attendance_day_v2_session_id` in (' . implode(',', $session_ids) . ')
              GROUP BY asds.`student_admission_id`, asds.attendance_day_v2_session_id) AS asMain
        JOIN attendance_std_day_v2_session ads ON ads.id = asMain.attendance_day_v2_session_id
        JOIN class_section cs ON cs.id = ads.class_section_id
        JOIN class c ON c.id = cs.class_id
        GROUP BY c.id, ads.class_section_id, as1_status
        ORDER BY c.class_name, cs.section_name')->result_array();
    }

    // Build comprehensive class-wise data including sections without attendance
    $class_wise_data = [];
    $total_present = 0;
    $total_absent = 0;

    // First, create structure for all sections
    foreach ($all_sections as $section) {
      $class_key = $section['class_id'] . '_' . $section['class_name'];
      $section_key = $section['class_section_id'] . '_' . $section['section_name'];

      if (!isset($class_wise_data[$class_key])) {
        $class_wise_data[$class_key] = [
          'class_id' => $section['class_id'],
          'class_name' => $section['class_name'],
          'sections' => [],
          'total_present' => 0,
          'total_absent' => 0
        ];
      }

      $class_wise_data[$class_key]['sections'][$section_key] = [
        'section_id' => $section['class_section_id'],
        'section_name' => $section['section_name'],
        'present' => 0,
        'absent' => 0,
        'attendance_taken' => in_array($section['class_section_id'], $sections_with_attendance)
      ];
    }

    // Then, populate attendance data for sections where it was taken
    foreach ($attendance_data as $row) {
      $class_key = $row['class_id'] . '_' . $row['class_name'];
      $section_key = $row['class_section_id'] . '_' . $row['section_name'];

      if (isset($class_wise_data[$class_key]['sections'][$section_key])) {
        if ($row['type'] == 'P') {
          $class_wise_data[$class_key]['sections'][$section_key]['present'] = $row['att_count'];
          $class_wise_data[$class_key]['total_present'] += $row['att_count'];
          $total_present += $row['att_count'];
        } elseif ($row['type'] == 'A') {
          $class_wise_data[$class_key]['sections'][$section_key]['absent'] = $row['att_count'];
          $class_wise_data[$class_key]['total_absent'] += $row['att_count'];
          $total_absent += $row['att_count'];
        }
      }
    }

    return [
      'type' => 'attendance_class_wise',
      'data' => $class_wise_data,
      'present' => $total_present,
      'absent' => $total_absent,
      'total_sections' => count($all_sections),
      'sections_with_attendance' => count($sections_with_attendance),
      'sections_pending' => count($all_sections) - count($sections_with_attendance)
    ];
  }

  private function _getSectionWiseAttendanceData($today, $sectionIds)
  {
    // Get ALL sections that the user has access to (whether attendance taken or not)
    $all_sections = $this->db_readonly->select("cs.id as class_section_id, c.class_name, cs.section_name")
      ->from('class_section cs')
      ->join('class c', 'c.id = cs.class_id')
      ->where_in('cs.id', $sectionIds)
      ->order_by('c.class_name, cs.section_name')
      ->get()->result_array();

    // Get attendance sessions for today
    $sessions = $this->db_readonly->select("ads.id, ads.class_section_id")
      ->from('attendance_std_day_v2_session ads')
      ->where('ads.att_taken_date', $today)
      ->where_in('ads.class_section_id', $sectionIds)
      ->get()->result_array();

    $session_ids = array_column($sessions, 'id');
    $sections_with_attendance = array_column($sessions, 'class_section_id');

    // Get attendance data for sections where attendance was taken
    $attendance_data = [];
    if (!empty($session_ids)) {
      $attendance_data = $this->db_readonly->query('SELECT ads.class_section_id, c.class_name, cs.section_name,
                                                           as1_status as type, count(as1_status) as att_count
        FROM (SELECT asds.student_admission_id, asds.attendance_day_v2_session_id,
                     IF((asds.morning_session_status + asds.afternoon_session_status) = 0, "A", "P") as as1_status
              FROM `attendance_std_day_v2_students` AS asds
              WHERE asds.`attendance_day_v2_session_id` in (' . implode(',', $session_ids) . ')
              GROUP BY asds.`student_admission_id`, asds.attendance_day_v2_session_id) AS asMain
        JOIN attendance_std_day_v2_session ads ON ads.id = asMain.attendance_day_v2_session_id
        JOIN class_section cs ON cs.id = ads.class_section_id
        JOIN class c ON c.id = cs.class_id
        GROUP BY ads.class_section_id, as1_status
        ORDER BY c.class_name, cs.section_name')->result_array();
    }

    // Build comprehensive section-wise data
    $section_wise_data = [];
    $total_present = 0;
    $total_absent = 0;

    // First, create structure for all sections
    foreach ($all_sections as $section) {
      $section_wise_data[] = [
        'section_id' => $section['class_section_id'],
        'class_name' => $section['class_name'],
        'section_name' => $section['section_name'],
        'present' => 0,
        'absent' => 0,
        'attendance_taken' => in_array($section['class_section_id'], $sections_with_attendance)
      ];
    }

    // Then, populate attendance data for sections where it was taken
    foreach ($attendance_data as $row) {
      foreach ($section_wise_data as &$section) {
        if ($section['section_id'] == $row['class_section_id']) {
          if ($row['type'] == 'P') {
            $section['present'] = $row['att_count'];
            $total_present += $row['att_count'];
          } elseif ($row['type'] == 'A') {
            $section['absent'] = $row['att_count'];
            $total_absent += $row['att_count'];
          }
          break;
        }
      }
    }

    return [
      'type' => 'attendance_section_wise',
      'data' => $section_wise_data,
      'present' => $total_present,
      'absent' => $total_absent,
      'total_sections' => count($all_sections),
      'sections_with_attendance' => count($sections_with_attendance),
      'sections_pending' => count($all_sections) - count($sections_with_attendance)
    ];
  }

  public function getNonReconStatus()
  {

    $date = strtotime("7 day");
    $pDate = date('Y-m-d', $date);
    $cDate = date('Y-m-d');
    $recnCount = $this->db_readonly->query("select count(id) as recCount from fee_transaction_installment where reconciliation_status = '1' ")->row();

    $this->db_readonly->select("count(id) as recChque");
    $this->db_readonly->from('fee_transaction_installment');
    $this->db_readonly->where('date_format(cheque_or_dd_date,"%Y-%m-%d") BETWEEN "' . $cDate . '" and "' . $pDate . '"');
    $this->db_readonly->where('reconciliation_status', '1');
    $this->db_readonly->where('payment_type', '4');
    $oneWeek = $this->db_readonly->get()->row();

    return array('reconCount' => $recnCount, 'chCount' => $oneWeek);
  }

  public function circular_data_desktop($studentId)
  {
    $this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y') as date ,circular_title,circular_content,stakeholder_id as student_id,category");
    $this->db_readonly->from('circular_master');
    $this->db_readonly->join('circular_sent_to', "circular_sent_to.circular_id=circular_master.id and circular_sent_to.stakeholder_id=$studentId and circular_sent_to.avatar_type=1");
    $this->db_readonly->where('is_published', 1);
    $this->db_readonly->where('circular_master.acad_year_id', $this->yearId);
    $this->db_readonly->limit(7);
    $this->db_readonly->order_by('circular_master.id', 'desc');
    // $this->db_readonly->where('category',$category);
    return $this->db_readonly->get()->result();
  }

  public function circular_data_desktop_new($parentId) {
    $this->db_readonly->select("cm.id, sent_on,title,body,stakeholder_id as parent_id,category, ct.is_read, cm.mode, is_read");
    $this->db_readonly->from('circularv2_master cm');
    $this->db_readonly->join('circularv2_sent_to ct', "ct.circularv2_master_id=cm.id and ct.stakeholder_id=$parentId and ct.avatar_type=2");
    $this->db_readonly->where('visible', 1);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->limit(7);
    $this->db_readonly->order_by('cm.id', 'desc');
    return $this->db_readonly->get()->result();
  }

  public function circular_data_desktop_for_staff($staffId)
  {
    $this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y') as date ,circular_title,circular_content,stakeholder_id as student_id,category");
    $this->db_readonly->from('circular_master');
    $this->db_readonly->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id', 'left');
    $this->db_readonly->where('circular_sent_to.stakeholder_id', $staffId);
    $this->db_readonly->where('circular_sent_to.avatar_type', 4);
    $this->db_readonly->where('is_published', 1);
    $this->db_readonly->limit(7);
    $this->db_readonly->order_by('circular_master.id', 'desc');
    $result = $this->db_readonly->get()->result();
    // echo '<pre>';print_r($result);
    return $result;
  }

  public function getCircularsAndEmails($stakeholder_id, $avatar_type) {
    $this->db_readonly->select("cm.id, sent_on, title, body, category, cm.file_path, cm.mode, is_read");
    $this->db_readonly->from('circularv2_master cm');
    $this->db_readonly->join('circularv2_sent_to cs', 'cs.circularv2_master_id=cm.id');
    $this->db_readonly->where('cm.visible', 1);
    $this->db_readonly->where('cs.stakeholder_id', $stakeholder_id);
    $this->db_readonly->where('cs.avatar_type', $avatar_type);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->limit(5);
    $this->db_readonly->order_by('cm.sent_on', 'desc');
    return $this->db_readonly->get()->result();
    }

  public function homework_data_desktop_for_parent($student_id, $sectionId)
  {
    $this->db_readonly->select("h.*");
    $this->db_readonly->from('homework h');
    $this->db_readonly->join('class_section cs', 'cs.id = h.section_id', 'left');
    $this->db_readonly->join('student_year sy', 'sy.class_section_id = cs.id', 'left');
    $this->db_readonly->join('student_admission sa', 'sy.student_admission_id = sa.id', 'left');
    $this->db_readonly->where('sy.acad_year_id', $this->yearId ); 
    $this->db_readonly->where('h.section_id', $sectionId); 
    $this->db_readonly->where('sa.id', $student_id); 
    $this->db_readonly->order_by('h.id', 'desc');
    $result = $this->db_readonly->get()->row_array();
    // echo '<pre>';print_r($result);
    // echo $this->db->last_query(); die();
    return $result;
  }

  public function get_upcoming_holidays()
  {
    $today = date('Y-m-d');
    $sql = "SELECT event_name, from_date, date_format(from_date,'%d-%m-%Y') as fdate, date_format(to_date,'%d-%m-%Y') as tdate ,event_type FROM school_calender WHERE  (from_date >='$today' OR to_date >='$today') AND applicable_to!=1 ORDER BY from_date LIMIT 5";
    $query = $this->db_readonly->query($sql);
    return $query->result();
  }

  public function dontShow()
  {
    $avatarId = $this->authorization->getAvatarId();
    $userId = $this->db->select('u.id')->from('users u')->join('avatar a', 'a.user_id=u.id')->where('a.id', $avatarId)->get()->row()->id;
    $this->db->where('id', $userId);
    return $this->db->update('users', array('donot_show' => 1));
  }

  public function getStaffProfileData($staff_id) {
    $result =  $this->db_readonly->select("sm.*, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, staff_type")->where('id', $staff_id)->get('staff_master sm')->row();
    if(!empty($result)) {
      $staffType = $this->settings->getSetting('staff_type');
      $result->staffType = '';
      if (!empty($staffType)) {
        foreach ($staffType as $key => $value) {
          if ($result->staff_type == $key) {
            $result->staffType = $value;
          }
        }
      }
     
      // if($result->staff_type == 0) {
      //   $result->staffType = 'Teaching';
      // } else if($result->staff_type == 1) {
      //   $result->staffType = 'Non-Teaching';
      // } else if($result->staff_type == 2) {
      //   $result->staffType = 'Board Member';
      // } else if($result->staff_type == 3) {
      //   $result->staffType = 'Supporting Staff';
      // } 
    }
    
    return $result;
  }

  public function getSMSInfo($staffId)
  {
    $this->db_readonly->select('sm.id,sm.sms_date,sm.sms_content');
    $this->db_readonly->from('sms_master sm');
    $this->db_readonly->join('sms_sent_to st', 'st.sms_id=sm.id');
    $this->db_readonly->where('st.student_staff_id', $staffId);
    $this->db_readonly->where('sm.user_type', 'Staff');

    $this->db_readonly->order_by('sm.sms_date', 'desc');
    $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function getTextInfo($staffId)
  {
    $date = date('Y-m-d');
    $this->db_readonly->select('count(tm.id) as textCount');
    $this->db_readonly->from('texting_master tm');
    $this->db_readonly->join('text_sent_to ts', 'ts.texting_master_id=tm.id');
    $this->db_readonly->where('ts.stakeholder_id', $staffId);
    $this->db_readonly->where('ts.avatar_type', 4);
    $this->db_readonly->where("DATE_FORMAT(tm.sent_on, '%Y-%m-%d')='$date'");
    // $this->db_readonly->order_by('tm.sent_on', 'desc');
    // $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->row();
    return $result;
  }

  public function getSTBOpenTasks($staffId) {
    $today = date("Y-m-d");
    
		$due_tasks_count = $this->db_readonly->select('COUNT(*) as due_date_count')
      ->from('stb_mytasks')
      ->where("DATE_FORMAT(due_date,'%Y-%m-%d') = '$today'")
      ->where('created_by', $staffId)
      ->where('status', 1)
      ->where('is_deleted', 0)
      ->get()
      ->row()
      ->due_date_count;

    return $due_tasks_count;
  }

  public function getCircularInfo($staffId)
  {
    $this->db_readonly->select('cm.id,cm.circular_date,cm.circular_title');
    $this->db_readonly->from('circular_master cm');
    $this->db_readonly->join('circular_sent_to ct', 'ct.circular_id=cm.id');
    $this->db_readonly->where('ct.stakeholder_id', $staffId);
    $this->db_readonly->where('cm.is_published', 1);
    $this->db_readonly->where('ct.avatar_type', 4);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('cm.circular_date', 'desc');
    $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->result();
    return $result;
    // echo "<pre>";
    // print_r($result);die();
  }

  public function getNewCircularInfo($staffId)
  {
    // $date = date('Y-m-d');
    $this->db_readonly->select('count(cm.id) as circularCount');
    $this->db_readonly->from('circularv2_master cm');
    $this->db_readonly->join('circularv2_sent_to ct', 'ct.circularv2_master_id=cm.id');
    $this->db_readonly->where('ct.stakeholder_id', $staffId);
    $this->db_readonly->where('cm.visible', 1);
    $this->db_readonly->where('ct.avatar_type', 4);
    $this->db_readonly->where('ct.is_read', 0);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    // $this->db_readonly->where("date_format(cm.sent_on, '%Y-%m-%d')='$date'");
    // $this->db_readonly->order_by('cm.sent_on', 'desc');
    // $this->db_readonly->limit(5);
    $result = $this->db_readonly->get()->row();
    return $result;
    // echo "<pre>";
    // print_r($result);die();
  }

  
  public function get_sections_school()
  {
    $this->db_readonly->distinct()->select('section_name')->where('is_placeholder', 0);
    if ($this->yearId != '') {
      $this->db_readonly->where("class_id in (select id from class where acad_year_id=$this->yearId)");
    }
    return $this->db_readonly->get('class_section')->result();
  }

  public function getStaffData($staffId)
  {
    return $this->db->select('*')->where('id', $staffId)->get('staff_master')->row();
  }

  public function get_overall_fee_record($bpId, $student_admission_status){
    $this->db_readonly->select("sa.id as student_id, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status");
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->join('student_year sy', "sa.id = sy.student_admission_id");
    $this->db_readonly->where("sy.acad_year_id", $this->yearId);
    $this->db_readonly->where_not_in("sy.promotion_status", ['JOINED']);
    $studentData = $this->db_readonly->get()->result();
    
    $studentIds = [];
    foreach ($studentData as $val) {
      if(!empty($student_admission_status)){
        if (in_array($val->admission_status, $student_admission_status)) {
          $studentIds[] = $val->student_id;
        }
      }else{
        $studentIds[] = $val->student_id;
      }
    }
  
    if (empty($studentIds)) {
      return false;
    }

    $this->db_readonly->select("sum(fss.total_fee) as fee_amount, (ifnull(sum(fss.total_fee_paid),0) - ifnull(sum(fss.discount),0)) as paid_amount, (sum(fss.total_fee) - ifnull(sum(fss.total_fee_paid),0) - (ifnull(sum(fss.total_concession_amount),0)  + ifnull(sum(fss.total_concession_amount_paid),0))) as balance, (ifnull(sum(fss.total_concession_amount),0)  + ifnull((sum(fss.total_concession_amount_paid)),0)) as concession, ifnull(sum(fss.discount),0)  as discount, ifnull(sum(fss.total_fine_amount),0) as total_fine_amount, count(fcs.id) as count");
    $this->db_readonly->from('feev2_cohort_student fcs');
    $this->db_readonly->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id');
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    if ($bpId) {
      $this->db_readonly->where_in('fb.id', $bpId);
    }
    $this->db_readonly->where_in('fcs.student_id', $studentIds);
    $this->db_readonly->where('fb.acad_year_id', $this->yearId);
    $this->db_readonly->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id');
    // $this->db->join('student_admission sa', 'fcs.student_id=sa.id');
    $total =  $this->db_readonly->get()->row();
    $this->db_readonly->select("sum(ifnull(fsi.refund_amount,0)) as refund_amount, count(fcs.id) as count");
    $this->db_readonly->from('feev2_cohort_student fcs');
    $this->db_readonly->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id');
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    if ($bpId) {
      $this->db_readonly->where_in('fb.id', $bpId);
    }
    $this->db_readonly->where_in('fcs.student_id', $studentIds);
    $this->db_readonly->where('fb.acad_year_id', $this->yearId);
    $this->db_readonly->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id');
    $this->db_readonly->join('feev2_student_installments fsi', 'fss.id=fsi.fee_student_schedule_id');
    // $this->db->join('student_admission sa', 'fcs.student_id=sa.id');
    $refundtotal =  $this->db_readonly->get()->row();

    $this->db_readonly->select("ifnull(sum(ft.amount_paid),0) + ifnull(sum(ft.concession_amount),0) as reon_amount");
    $this->db_readonly->from('feev2_transaction ft');
    $this->db_readonly->join('feev2_student_schedule fss', 'ft.fee_student_schedule_id=fss.id');
    $this->db_readonly->join('feev2_cohort_student fcs', 'fss.feev2_cohort_student_id=fcs.id');
    if ($bpId) {
      $this->db_readonly->where_in('fcs.blueprint_id', $bpId);
    }
    $this->db_readonly->where_in('fcs.student_id', $studentIds);
    $this->db_readonly->where('ft.acad_year_id',$this->yearId);
    $this->db_readonly->join('feev2_transaction_payment ftp', 'ft.id=ftp.fee_transaction_id');
    $this->db_readonly->where('ftp.reconciliation_status', '1');
    $transcation = $this->db_readonly->get()->row();

    $this->db_readonly->select('fcs.student_id')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
    ->where('fb.acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    if ($bpId) {
      $this->db_readonly->where_in('fcs.blueprint_id',$bpId);
    }
    $this->db_readonly->where_in('fcs.student_id', $studentIds);
    $this->db_readonly->group_by('fcs.student_id');
    $feeQuery = $this->db_readonly->get()->result();

    $stdIds = [];
    foreach ($feeQuery as $key => $val) {
      array_push($stdIds, $val->student_id);
    }
    if(empty($stdIds)){
      return false;
    }
    $prevousYearId = $this->yearId - 1;
    $prevousYearname= $this->acad_year->getAcadYearById($prevousYearId);
    $this->db_readonly->select("sum(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0)) as balance");
    $this->db_readonly->from('feev2_blueprint fb');
    $this->db_readonly->where('fb.acad_year_id',$prevousYearId);
    $this->db_readonly->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id');
    $this->db_readonly->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
    $this->db_readonly->where('fss.payment_status!=','FULL');
    $this->db_readonly->where_in('fcs.student_id',$stdIds);
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    $this->db_readonly->where_in('fcs.student_id', $studentIds);
    $previousBalance =  $this->db_readonly->get()->row();
    $openingBalance = 0;
    if(!empty($previousBalance)  && !empty($previousBalance->balance)){
      $openingBalance = $previousBalance->balance;
    }
    $additionalAmount = $this->db_readonly->select("sum(total_amount- total_used_amount - excess_refund_amount) as addtAmount")
    ->from('feev2_additional_amount fdm')
    ->where_in('fdm.student_id',$stdIds)
    ->get()->row();
    $excess = 0;
    if(!empty($additionalAmount) && !empty($additionalAmount->addtAmount)){
      $excess = $additionalAmount->addtAmount;
    }
    return array('fee_paid' => $total->paid_amount, 'recon_amount' => $transcation->reon_amount, 'Total_fee' => $total->fee_amount, 'balance' => $total->balance, 'concession' => $total->concession,'refund_amount'=>$refundtotal->refund_amount,'previousBalance'=>$openingBalance,'excess_amount'=>$excess,'prevousYearname'=>$prevousYearname);
  }

  public function get_overall_fee_record_class_wise($bpId, $from_date, $to_date){
    
    $this->db_readonly->select("sy.class_id as class_id, sum(ft.amount_paid) as total_collected_amount, sum(ft.concession_amount) as concession, sum(ifnull(ft.card_charge_amount,0)) as card_charge_amount, sum(ifnull(ft.fine_amount,0)) as fine_amount,  fcs.blueprint_id as bpId")
    ->from('feev2_student_schedule fss')
    ->join('feev2_cohort_student fcs','fcs.id=fss.feev2_cohort_student_id')
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('feev2_transaction ft',"fss.id=ft.fee_student_schedule_id and ft.status='SUCCESS' and ft.soft_delete!=1")
    ->group_by('sy.class_id')
    ->order_by('sy.class_id');
    if ($from_date && $to_date) {
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    if ($bpId) {
      $this->db_readonly->where_in('fcs.blueprint_id',$bpId);
    }
    $transCount = $this->db_readonly->get()->result();
    $transArry = [];
    foreach ($transCount as $key => $val) {
      $transArry[$val->class_id] = $val;
    }
    $classes = $this->db_readonly->select('id, class_name')->where('is_placeholder!=1')->where('acad_year_id',$this->yearId)->get('class')->result();
    $resArryy = [];
    foreach ($classes as $key => $val) {
      $summary  = new stdClass();
      if (array_key_exists($val->id, $transArry)) {
        $summary->class_name = $val->class_name;
        $summary->total_collected_amount = $transArry[$val->id]->total_collected_amount;
        $summary->concession = $transArry[$val->id]->concession;
      }else{
        $summary->class_name = $val->class_name;
        $summary->total_collected_amount = 0;
        $summary->concession = 0;
      }
      array_push($resArryy, $summary);
    }
    return $resArryy;

  }

  public function get_overall_fee_record_date_wise($bpId, $from_date, $to_date){
    $this->db_readonly->select("sum(ft.amount_paid) as total_collected_amount, sum(ft.concession_amount) as concession, sum(ifnull(ft.card_charge_amount,0)) as card_charge_amount, sum(ifnull(ft.fine_amount,0)) as fine_amount,  fcs.blueprint_id as bpId, date_format(ft.paid_datetime,'%d-%b') as paidDate")
    ->from('feev2_transaction ft')
    ->where('ft.status','SUCCESS')
    ->where('ft.soft_delete!=','1')
    ->join('feev2_student_schedule fss','fss.id=ft.fee_student_schedule_id')
    ->join('feev2_cohort_student fcs',"fss.feev2_cohort_student_id=fcs.id")
    ->group_by("date_format(ft.paid_datetime,'%d-%m-%Y')")
    ->order_by('ft.paid_datetime','desc');
    $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"');
    if ($bpId) {
      $this->db_readonly->where_in('fcs.blueprint_id',$bpId);
    }
    $dateArry = array();         
    $toDate = strtotime($to_date); 
    $fromDate = strtotime($from_date);
    for ($currentDate = $toDate; $currentDate <= $fromDate; $currentDate += (86400)) {                         
      $Store = date('d-M', $currentDate); 
      $dateArry[$Store] = array('total_collected_amount'=>"0",'paidDate'=>$Store,'concession'=>"0",'card_charge_amount'=>"0",'fine_amount'=>"0"); 
    }
    $transCount = $this->db_readonly->get()->result();
    foreach ($transCount as $key => $val) {
      if (array_key_exists($val->paidDate, $dateArry)) {
        $dateArry[$val->paidDate] = $val;
      }
    }
    return $dateArry;

  }

  public function get_library_trend_details_date_wise($from_date, $to_date) {
    $borrow_counts = $this->db_readonly->select("date_format(issue_date,'%d-%b') as issue_date, count(id) as books_count")
      ->from('library_transacation')
      ->where('date_format(issue_date,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"')
      ->where('status', '1')
      ->where('issue_date is not null')
      ->group_by("date_format(issue_date,'%d-%m-%Y')")
      ->order_by('issue_date','desc')
      ->get()->result();

    $return_counts = $this->db_readonly->select("date_format(return_date,'%d-%b') as return_date, count(id) as books_count")
      ->from('library_transacation')
      ->where('date_format(return_date,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"')
      ->where('status', '2')
      ->where('return_date is not null')
      ->group_by("date_format(return_date,'%d-%m-%Y')")
      ->order_by('return_date','desc')
      ->get()->result();

    //Initialize the date array
    $books_date_array = array();         
    $to_date_str = strtotime($to_date); 
    $form_date_str = strtotime($from_date);
    for ($temp_date = $to_date_str; $temp_date <= $form_date_str; $temp_date += (86400)) {                         
      $temp_date_mod = date('d-M', $temp_date);
      $books_date_array[$temp_date_mod] = array('borrow_book_count'=>"0",'issue_date'=>$temp_date_mod,'return_book_count'=>"0"); 
    }
    foreach ($borrow_counts as $val1) {
      if (array_key_exists($val1->issue_date, $books_date_array)) {
        $books_date_array[$val1->issue_date]['borrow_book_count'] = $val1->books_count;
      }
    }
    foreach ($return_counts as $val2) {
      if (array_key_exists($val2->return_date, $books_date_array)) {
        $books_date_array[$val2->return_date]['return_book_count'] = $val2->books_count;
      }
    }

    return $books_date_array;
  }

  public function get_staff_leave_trend_details_date_wise($from_date, $to_date) {
    $from_leave_counts = $this->db_readonly->select("date_format(from_date,'%d-%b') as leave_date, count(id) as leaves_count")
      ->from('leave_v2_staff')
      ->where('date_format(from_date,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"')
      ->where('status not in (3,4)')
      ->group_by("date_format(from_date,'%d-%m-%Y')")
      ->order_by('from_date')
      ->get()->result();

    $to_leave_counts = $this->db_readonly->select("date_format(from_date,'%d-%b') as leave_date, count(id) as leaves_count")
      ->from('leave_v2_staff')
      ->where('date_format(from_date,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"')
      ->where('status not in (3,4)')
      ->group_by("date_format(from_date,'%d-%m-%Y')")
      ->order_by('from_date')
      ->get()->result();

    //Initialize the date array
    $leaves_date_array = array();         
    $to_date_str = strtotime($to_date); 
    $form_date_str = strtotime($from_date);
    for ($temp_date = $to_date_str; $temp_date <= $form_date_str; $temp_date += (86400)) {                         
      $temp_date_mod = date('d-M', $temp_date);
      $leaves_date_array[$temp_date_mod] = array('leaves_count'=>"0",'leave_date'=>$temp_date_mod); 
    }
    foreach ($from_leave_counts as $val1) {
      if (array_key_exists($val1->leave_date, $leaves_date_array)) {
        $leaves_date_array[$val1->leave_date]['leaves_count'] = $val1->leaves_count;
      }
    }
    foreach ($to_leave_counts as $val1) {
      if (array_key_exists($val1->leave_date, $leaves_date_array)) {
        $leaves_date_array[$val1->leave_date]['leaves_count'] = $leaves_date_array[$val1->leave_date]['leaves_count'] + $val1->leaves_count;
      }
    }

    return $leaves_date_array;
  }

  public function get_ticket_summary($from_date, $to_date){
    $this->db_readonly->select("count(tm.id) as input_tickets,date_format(tm.created_on,'%d-%b') as created_on")
    ->from('ticketing_master tm')
    ->where('tm.status','Open')
    ->group_by("date_format(tm.created_on,'%d-%m-%Y')")
    ->order_by('tm.created_on','desc');
    $this->db_readonly->where('date_format(tm.created_on,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"');

    $dateArry = array();         
    $toDate = strtotime($to_date); 
    $fromDate = strtotime($from_date);
    for ($currentDate = $toDate; $currentDate <= $fromDate; $currentDate += (86400)) {                         
      $Store = date('d-M', $currentDate); 
      $dateArry[$Store] = array('input_tickets'=>"0",'created_on'=>$Store,); 
    }
    $transCount = $this->db_readonly->get()->result();
    foreach ($transCount as $key => $val) {
      if (array_key_exists($val->created_on, $dateArry)) {
        $dateArry[$val->created_on] = $val;
      }
    }
    return $dateArry;

  }

  public function get_internal_ticket_summary($from_date, $to_date){
    $this->db_readonly->select("count(itt.id) as internal_tickets,date_format(itt.created_on,'%d-%b') as created_on")
    ->from('internal_ticketing_items itt')
    ->where('itt.ticket_status','Open')
    ->group_by("date_format(itt.created_on,'%d-%m-%Y')")
    ->order_by('itt.created_on','desc');
    $this->db_readonly->where('date_format(itt.created_on,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"');

    $dateArry = array();         
    $toDate = strtotime($to_date); 
    $fromDate = strtotime($from_date);
    for ($currentDate = $toDate; $currentDate <= $fromDate; $currentDate += (86400)) {                         
      $Store = date('d-M', $currentDate); 
      $dateArry[$Store] = array('internal_tickets'=>"0",'created_on'=>$Store,); 
    }
    $transCount = $this->db_readonly->get()->result();
    foreach ($transCount as $key => $val) {
      if (array_key_exists($val->created_on, $dateArry)) {
        $dateArry[$val->created_on] = $val;
      }
    }
    return $dateArry;

  }

  public function get_visitor_count($from_date, $to_date){
    $this->db_readonly->select("count(iv.id) as visitor_count,date_format(iv.visit_datetime,'%d-%b') as visit_datetime")
    ->from('infirmary_visits iv')
    ->group_by("date_format(iv.visit_datetime,'%d-%m-%Y')")
    ->order_by('iv.visit_datetime','desc');
    $this->db_readonly->where('date_format(iv.visit_datetime,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"');

    $dateArry = array();         
    $toDate = strtotime($to_date); 
    $fromDate = strtotime($from_date);
    for ($currentDate = $toDate; $currentDate <= $fromDate; $currentDate += (86400)) {                         
      $Store = date('d-M', $currentDate); 
      $dateArry[$Store] = array('visitor_count'=>"0",'visit_datetime'=>$Store,); 
    }
    $transCount = $this->db_readonly->get()->result();
    foreach ($transCount as $key => $val) {
      if (array_key_exists($val->visit_datetime, $dateArry)) {
        $dateArry[$val->visit_datetime] = $val;
      }
    }
    return $dateArry;

  }

  public function getLatestStudentTasks($student_id, $count=5) {
    // $sql = "SELECT t.id, t.task_name, s.subject_name, ts.read_status, (CASE WHEN ts.submission_status=1 THEN 'Submitted' ELSE 'Not-submitted' END) AS submission_status, (CASE WHEN ts.evaluation_status=1 THEN 'Evaluated' ELSE 'Not-evaluated' END) AS evaluation_status 
    //         FROM lp_tasks t 
    //         JOIN lp_tasks_students ts ON ts.lp_tasks_id=t.id 
    //         JOIN lp_subjects s ON s.id=t.subject_id 
    //         WHERE ts.student_id=$student_id 
    //         LIMIT $count";
    $sql = "SELECT t.id, t.task_name, s.subject_name, DATE_FORMAT(t.task_last_date, '%d-%b') as last_date , DATE_FORMAT(t.created_on, '%d-%b') as assigned_date
            FROM lp_tasks t 
            JOIN lp_tasks_students ts ON ts.lp_tasks_id=t.id 
            JOIN lp_subjects s ON s.id=t.subject_id 
            WHERE ts.student_id=$student_id and t.status='published' and t.acad_year_id= $this->yearId 
            ORDER BY t.created_on 
            LIMIT $count";
    $data = $this->db_readonly->query($sql)->result();
    // foreach ($data as $key => $val) {
    //   $data[$key]->status = 'Unread';
    //   if($val->read_status == 'read') {
    //     $data[$key]->status = $val->submission_status;
    //     if($val->submission_status == 'Submitted') {
    //       $data[$key]->status = $val->evaluation_status;
    //     }
    //   }
    // }
    return $data;
  }

  public function getStudentCount() {

    $counts = $this->db_readonly->select("
        count(sa.id) as stdCount,
        SUM(CASE WHEN sy.admission_type = 1 AND sa.gender = 'M' THEN 1 ELSE 0 END) as reAdmissionBoys,
        SUM(CASE WHEN sy.admission_type = 1 AND sa.gender = 'F' THEN 1 ELSE 0 END) as reAdmissionGirls,
        SUM(CASE WHEN sy.admission_type = 2 AND sa.gender = 'M' THEN 1 ELSE 0 END) as newAdmissionBoys,
        SUM(CASE WHEN sy.admission_type = 2 AND sa.gender = 'F' THEN 1 ELSE 0 END) as newAdmissionGirls,
    ")
    ->from('student_admission sa')
    ->join('student_year sy', "sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId")
    ->where('sa.admission_status', 2)
    ->where("sy.promotion_status != 'JOINED'")
    ->where("sy.promotion_status != '4'")
    ->where("sy.promotion_status != '5'")
    ->get()
    ->row();
    // echo "<pre>";print_r($counts);die();
    $data = (array) $counts;

    return $data;
  }

  private function convert_object_to_string($object_data) {
    $arr= [];
    foreach($object_data as $key => $val) {
      array_push($arr, $val->student_id);
    }
    return $arr;
  }

  public function student_tracking_count(){
    $date= $_POST['date'];
    // Transport data
    $transport_query= "select distinct(sa.id) as student_id, 'Transport' as checkin_type from student_admission sa
      join tx_attendance txa on REPLACE(LTRIM(REPLACE(sa.rfid_number,'0',' ')),' ','0') = REPLACE(LTRIM(REPLACE(txa.rfid,'0',' ')),' ','0')
      where date_format(txa.updated_at, '%d-%m-%Y') = '$date' and  txa.journey_type= 'PICKING'";
    $transport_count= $this->db_readonly->query($transport_query)->result();

    // Getting student ids in array form
    $transport_ids_arr= $this->convert_object_to_string($transport_count);
    $avoid_str= '';
    if( !empty($transport_ids_arr) ) {
      $str= implode(',', $transport_ids_arr);
      $avoid_str= "and person_id not in ($str)";
    }

    // Gate data
    $gate_query= "select distinct(person_id) as student_id, 'Gate' as checkin_type from escort_master 
        where person_type='Student' and date_format(checkin_timestamp, '%d-%m-%Y') = '$date' $avoid_str";
    $gate_count= $this->db_readonly->query($gate_query)->result();

    $gate_ids_arr= array_merge($this->convert_object_to_string($gate_count), $transport_ids_arr);
    $avoid_str= '';
    if( !empty($gate_ids_arr) ) {
      $str= implode(',', $gate_ids_arr);
      $avoid_str= "and source_id not in ($str)";
    }

    // RFID device data
    $rfid_device_query= "select distinct(source_id) as student_id, 'Device' as checkin_type from rfid_logs 
        where source='Student' and date_format(punch_time, '%d-%m-%Y') = '$date' $avoid_str";
    $rfid_count= $this->db_readonly->query($rfid_device_query)->result();

    $rfid_ids_arr= array_merge($this->convert_object_to_string($rfid_count), $gate_ids_arr);
    $avoid_str= '';
    if( !empty($rfid_ids_arr) ) {
      $str= implode(',', $rfid_ids_arr);
      $avoid_str= "and sa.id not in ($str)";
    }

    // Biometric device data
    $biometric_device_query= "select distinct(sa.id) as student_id, 'Device' as checkin_type from student_admission sa
      join student_attendance_logs sal on sal.student_biometric_code= sa.student_biometric_code
      where date_format(sal.punch_time, '%d-%m-%Y') = '$date' $avoid_str";
      $biometric_count= $this->db_readonly->query($biometric_device_query)->result();

      // Total students
      $t_s_c= $this->db_readonly->select("count(sa.id) as total")
          ->from('student_admission sa')
          ->join('student_year sy', "sa.id= sy.student_admission_id")
          ->where("sa.admission_status= '2'")
          ->where("sy.promotion_status not in ('4', 'JOINED', '5')")
          ->where("sy.acad_year_id= $this->yearId")
          ->get()->row()->total;

      $student_tracking['att_device_count']=count($biometric_count) + count($rfid_count);
      $student_tracking['att_escort_master_count']=count($gate_count);
      $student_tracking['att_tx_attendance_count']=count($transport_count);
      $student_tracking['total_student']= $t_s_c;
      $student_tracking['total_checkin']=count($transport_count) + count($biometric_count) + count($rfid_count) + count($gate_count);
      
      // echo '<pre>'; print_r($student_tracking); die();
      return $student_tracking;
      
  }

  public function getStaffCount() {
    // return $this->db_readonly->select("count(sm.id) as stfCount, sum(case when staff_type=0 then 1 else 0 end) as teaching")
    $result= $this->db_readonly->select("count(sm.id) as stfCount, sm.staff_type")
    ->from('staff_master sm')
    ->group_by('sm.staff_type')
    ->where('sm.status', 2)
    ->where_in('sm.is_primary_instance', 1)
    ->get()->result();
    
    $staff_type = $this->settings->getSetting('staff_type');
    if(empty($staff_type)){
      return array((object)array('staff_type_name'=> 'staff_type_config_not_enabled', 'stfCount'=>0));
    }else{
      $staff_type_keys = array_keys($staff_type);
      
      if (!empty($staff_type) && !empty($result)) {
        foreach ($result as $key => &$val) {
          if (empty($val->staff_type)){
            if ($val->staff_type == "0") {
              $val->staff_type_name = $staff_type[$val->staff_type];
            } else {
              $val->staff_type_name = 'Unassigned';
            }
          }else if(!in_array($val->staff_type, $staff_type_keys)){
            // If changed in config and updated in staff details
            $val->staff_type_name = "NA";
          }else{
            $val->staff_type_name = $staff_type[$val->staff_type];
          }
        }
      }
      return $result;
    }
  }

  public function getCircularCount()
  {
    $avatar_type = $this->authorization->getAvatarType();
    $stakeholder_id = $this->authorization->getAvatarStakeHolderId();
    $unread = $this->db_readonly->select('count(cm.id) as unreadCount')
    ->from('circularv2_master cm')
    ->join('circularv2_sent_to ct', 'ct.circularv2_master_id=cm.id')
    ->where('ct.stakeholder_id', $stakeholder_id)
    ->where('cm.visible', 1)
    ->where('ct.avatar_type', $avatar_type)
    ->where('ct.is_read', 0)
    ->where('cm.acad_year_id', $this->yearId)
    ->get()->row()->unreadCount;

    $today = date('Y-m-d');

    $todays = $this->db_readonly->select('count(cm.id) as todaysCount')
    ->from('circularv2_master cm')
    ->join('circularv2_sent_to ct', 'ct.circularv2_master_id=cm.id')
    ->where('ct.stakeholder_id', $stakeholder_id)
    ->where('cm.visible', 1)
    ->where('ct.avatar_type', $avatar_type)
    ->where('ct.is_read', 0)
    ->where("DATE_FORMAT(cm.sent_on, '%Y-%m-%d')='$today'")
    ->where('cm.acad_year_id', $this->yearId)
    ->get()->row()->todaysCount;

    return array('unread' => $unread, 'todays' => $todays);
  }

  public function getSMSCount()
  {
    $avatar_type = $this->authorization->getAvatarType();
    $stakeholder_id = $this->authorization->getAvatarStakeHolderId();

    $sql = "SELECT count(id) as textCount 
        from text_sent_to ts 
        where stakeholder_id=$stakeholder_id 
        and avatar_type=$avatar_type 
        and is_read=0";
    $unread =  $this->db_readonly->query($sql)->row()->textCount;

    $date = date('Y-m-d');
    $sql = "SELECT count(id) as textCount 
        from text_sent_to ts 
        where texting_master_id in 
        (select id from texting_master where DATE_FORMAT(sent_on, 'Y-m-d')='$date')";
    $todays =  $this->db_readonly->query($sql)->row()->textCount;
    return array('unread' => $unread, 'todays' => $todays);
  }

  public function getStaffReportingData()
  {
    $stakeholder_id = $this->authorization->getAvatarStakeHolderId();
    $sql = "select sm1.id as staff_id, CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS staff_name, sm2.id as reporting_staff_id, CONCAT(ifnull(sm2.first_name,''),' ', ifnull(sm2.last_name,'')) AS reporting_to, sm1.is_reporting_manager, sm1.designation 
            from staff_master sm1 
            left join staff_master sm2 on sm2.id=sm1.reporting_manager_id 
            where sm1.status=2 AND sm1.id = $stakeholder_id
            order by sm1.first_name";
    return $this->db_readonly->query($sql)->row();
  }

  public function getStudentTaskCount($student_id) {
    $sql = "SELECT count(ts.id) as unreadCount 
            FROM lp_tasks_students ts 
            JOIN lp_tasks t on ts.lp_tasks_id=t.id
            WHERE ts.student_id=$student_id 
            AND ts.read_status='unread'
            AND t.acad_year_id = $this->yearId";
    $unread = $this->db_readonly->query($sql)->row()->unreadCount;

    $today = date('Y-m-d');
    $sql = "SELECT count(t.id) as newCount  
            FROM lp_tasks t 
            JOIN lp_tasks_students ts ON ts.lp_tasks_id=t.id 
            WHERE ts.student_id=$student_id 
            AND DATE_FORMAT(t.created_on, '%Y-%m-%d')='$today' 
            AND ts.read_status='unread'
            AND t.acad_year_id = $this->yearId";
    $new = $this->db_readonly->query($sql)->row()->newCount;
    return array('unread' => $unread, 'new' => $new);
  }

  public function getTasksByStaff($staff_id) {
    $count = $_POST['count'];
    $yesterday = date('Y-m-d',strtotime("-1 days"));

    $sql = "SELECT t.id, t.task_name, s.subject_name, DATE_FORMAT(t.task_last_date, '%d-%b') as last_date, t.class_section_id, SUM(CASE WHEN ts.submission_status=1 THEN 1 ELSE 0 END) as submission_count, SUM(CASE WHEN ts.evaluation_status=1 THEN 1 ELSE 0 END) as evaluation_count, COUNT(ts.id) as total_students   
            FROM lp_tasks t 
            JOIN lp_tasks_students ts ON ts.lp_tasks_id=t.id 
            JOIN lp_subjects s ON s.id=t.subject_id 
            WHERE t.created_by=$staff_id 
            -- AND t.task_last_date>='$yesterday' 
            GROUP BY ts.lp_tasks_id 
            ORDER BY t.created_on DESC 
            LIMIT $count";
    $data = $this->db_readonly->query($sql)->result();

    if(!empty($data)) {
      $sec = "select id, class_name, section_name from class_section where class_id in (select id from class where acad_year_id=$this->yearId)";
      $sections = $this->db_readonly->query($sec)->result();
      $section_names = [];
      foreach ($sections as $key => $s) {
        $section_names[$s->id] = $s->class_name.''.$s->section_name;
      }

      foreach ($data as $k => $d) {
        $section_ids = json_decode($d->class_section_id);
        $sections = [];
        foreach ($section_ids as $i => $sec_id) {
          if(array_key_exists($sec_id, $section_names)) {
            $sections[]= $section_names[$sec_id];
          }
        }
        $data[$k]->section_names = implode(", ", $sections);
      }
    }
    return $data;
  }

  public function getTaskSummary($count) {
    $today = date('Y-m-d');
    $prev = date('Y-m-d',strtotime("-$count days"));
    $sql = "SELECT DATE_FORMAT(t.created_on, '%d-%b') as y, COUNT(t.id) as a  
            FROM lp_tasks t  
            WHERE DATE_FORMAT(t.created_on, '%Y-%m-%d')>='$prev' 
            AND DATE_FORMAT(t.created_on, '%Y-%m-%d')<='$today' 
            GROUP BY DATE_FORMAT(t.created_on, '%d-%b') 
            ORDER BY DATE_FORMAT(t.created_on, '%Y-%M-%d')";
    $data = $this->db_readonly->query($sql)->result();

    $tasks = [];
    for($i=$count-1; $i>=0; $i--) {
      $y = date('d-M',strtotime("-$i days"));
      $tasks[$y] = array('y' => $y, 'a' => 0);
    }

    foreach ($data as $key => $d) {
      if(array_key_exists($d->y, $tasks)) {
        $tasks[$d->y]['a'] = $d->a;
      }
    }

    return $tasks;
  }

  public function getStaffTaskSummary($count) {
    $staff_id = $this->authorization->getAvatarStakeHolderId();
    $sql = "select concat('T', right(concat('00000',cast(stb.id as char(5))), 5)) as display_task_id, datediff(curdate(), stb.created_on) as age, stb.task_title, status from stb_tasks stb join stb_staff sts on sts.stb_id=stb.id and sts.staff_id='$staff_id' where stb.status != 'verified' and stb.status != 'completed' order by stb.id desc limit 7;";
    $data = $this->db_readonly->query($sql)->result();

    return $data;
  }

  public function getCommunicationSummary($count, $mode) {
    $today = date('Y-m-d');
    $prev = date('Y-m-d',strtotime("-$count days"));

    $communications = [];
    for($i=$count-1; $i>=0; $i--) {
      $y = date('d-M',strtotime("-$i days"));
      $communications[$y] = array('sent_on' => $y, 'Sms' => 0, 'Notifications' =>0, 'Circulars' => 0, 'Emails' => 0);
    }

    if(in_array('all', $mode) || in_array('Sms', $mode) || in_array('Notifications', $mode)) {
      $text_sql = "SELECT SUM(CASE WHEN ts.mode=1 THEN 1 ELSE 0 END) AS notifications, SUM(CASE WHEN ts.mode=1 THEN 0 ELSE 1 END) AS sms, DATE_FORMAT(tm.sent_on, '%d-%b') AS sent_date 
          FROM texting_master tm 
          JOIN text_sent_to ts ON ts.texting_master_id=tm.id 
          WHERE DATE_FORMAT(tm.sent_on, '%Y-%m-%d')<='$today' 
          AND DATE_FORMAT(tm.sent_on, '%Y-%m-%d')>='$prev' 
          GROUP BY DATE_FORMAT(tm.sent_on, '%d-%b') 
          ORDER BY DATE_FORMAT(tm.sent_on, '%Y-%M-%d')";
      $texts = $this->db_readonly->query($text_sql)->result();

      foreach ($texts as $key => $text) {
        if(array_key_exists($text->sent_date, $communications)) {
          $communications[$text->sent_date]['Sms'] = $text->sms;
          $communications[$text->sent_date]['Notifications'] = $text->notifications;
        }
      }
    }

    if(in_array('all', $mode) || in_array('Circulars', $mode)) {
      $circular_sql = "SELECT count(cs.id) as circulars, DATE_FORMAT(cm.sent_on, '%d-%b') AS sent_date 
          FROM circularv2_master cm 
          JOIN circularv2_sent_to cs ON cs.circularv2_master_id=cm.id 
          AND DATE_FORMAT(cm.sent_on, '%Y-%m-%d')<='$today' 
          AND DATE_FORMAT(cm.sent_on, '%Y-%m-%d')>='$prev' 
          AND cm.visible=1 AND cm.is_approved=1 
          GROUP BY DATE_FORMAT(cm.sent_on, '%d-%b') 
          ORDER BY DATE_FORMAT(cm.sent_on, '%Y-%M-%d')";

      $circulars = $this->db_readonly->query($circular_sql)->result();

      foreach ($circulars as $key => $circular) {
        if(array_key_exists($circular->sent_date, $communications)) {
          $communications[$circular->sent_date]['Circulars'] = $circular->circulars;
        }
      }
    }

    if(in_array('all', $mode) || in_array('Emails', $mode)) {
      $email_sql = "SELECT count(es.id) as emails, DATE_FORMAT(em.sent_on, '%d-%b') AS sent_date 
          FROM email_master em 
          JOIN email_sent_to es ON es.email_master_id=em.id 
          AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')<='$today' 
          AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')>='$prev' 
          GROUP BY DATE_FORMAT(em.sent_on, '%d-%b') 
          ORDER BY DATE_FORMAT(em.sent_on, '%Y-%M-%d')";

      $emails = $this->db_readonly->query($email_sql)->result();

      foreach ($emails as $key => $email) {
        if(array_key_exists($email->sent_date, $communications)) {
          $communications[$email->sent_date]['Emails'] = $email->emails;
        }
      }
    }

    return $communications;
  }

  public function get_student_count_adm_status_wise(){
    $result = $this->db_readonly->select('count(sa.id) as saId, (case when sy.promotion_status = 4 or sy.promotion_status=5 then sy.promotion_status else sa.admission_status end) as admission_status')
    ->from('student_admission sa')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sy.promotion_status!=','JOINED')
    ->group_by('sa.admission_status, sy.promotion_status')
    ->get()->result();
    $countArry = [];
    $adm_status_type = $this->settings->getSetting('admission_status');
    if (!empty($adm_status_type) && !empty($result)) {
      foreach ($result as $key => &$val) {
        $val->adm_status_type_name = $adm_status_type[$val->admission_status];
      }
    }
    foreach ($result as $key => $val) {
      $countArry[$val->admission_status][] = $val->saId;
    }
    return $countArry;
  }

  // status and std count
   public function student_count_as_admission_status(){
    $result = $this->db_readonly->select('count(sa.id) as saIdCount, (case when sy.promotion_status = 4 or sy.promotion_status=5 then sy.promotion_status else sa.admission_status end) as admission_status')
    ->from('student_admission sa')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sy.promotion_status!=','JOINED')
    ->group_by('sa.admission_status, sy.promotion_status')
    ->get()->result();


    $adm_status_type = $this->settings->getSetting('admission_status');
    if (!empty($adm_status_type) && !empty($result)) {
      foreach ($result as $key => &$val) {
        $val->adm_status_type_name = $adm_status_type[$val->admission_status];
      }
    }
    
    return $result;
  }

   public function student_statistics_count(){
    $result =  $this->db_readonly->select("
      count(if(sa.gender='M',1,NULL)) 'boys_count', 
      count(if(sa.gender='F',1,NULL)) 'girls_count', 
      count(if(sy.is_rte='1',1,NULL)) 'rte_count', 
      count(if(sy.is_rte='2',1,NULL)) 'nonRTE_count',
      count(if(sy.is_rte='3',1,NULL)) 'scholarship_count',
      count(if(sa.has_staff='1',1,NULL)) 'staff_kids',
      sum(case when sy.admission_type= 2 then 1 else 0 end) as new_adm,
      sum(case when sy.admission_type= 1 then 1 else 0 end) as re_adm 
      ")
      ->from('student_admission sa')
      ->where('sa.admission_status','2')
      ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      ->where("sy.promotion_status!='JOINED'")
      ->where('sy.promotion_status!=','4')
      ->where('sy.promotion_status!=','5')
      ->get()->row();


    $result->fps = $result->nonRTE_count - $result->staff_kids;
    return $result;
  }

  public function infirmary_statistics_count(){
      $total =  $this->db_readonly->select("count(id) as total_count")
      ->from('infirmary_visits iv')
      ->where('iv.acad_year_id',$this->yearId)
      ->get()->row();

      $staff_count =  $this->db_readonly->select("count(id) as staff_count")
      ->from('infirmary_visits iv')
      ->where('iv.acad_year_id',$this->yearId)
      ->where('iv.visitor_type','staff')
      ->get()->row();
      
      $student_count =  $this->db_readonly->select("count(id) as student_count")
      ->from('infirmary_visits iv')
      ->where('iv.acad_year_id',$this->yearId)
      ->where('iv.visitor_type','student')
      ->get()->row();

      $hospitalization_count =  $this->db_readonly->select("count(id) as hospitalization_count")
      ->from('infirmary_hospitalization')
      ->where("acad_year_id",$this->yearId)
      ->get()->row();

      $activity_count =  $this->db_readonly->select("count(id) as activity_count")
      ->from('infirmary_activity')
      ->where("acad_year_id",$this->yearId)
      ->get()->row();

      $resArry = array(
        'total_count'=>$total->total_count,
        'staff_count'=>$staff_count->staff_count,
        'student_count'=>$student_count->student_count,
        'hospitalization_count'=>$hospitalization_count->hospitalization_count,
        'activity_count'=>$activity_count->activity_count,
      );

    return $resArry;
  }

  public function get_student_count_partial_deactivate(){
    $result = $this->db_readonly->select('count(sa.id) as partial_deactivate')
    ->from('student_admission sa')
    ->where('sa.temp_deactivation',1)
    ->get()->row();
    if (!empty($result)) {
      return $result->partial_deactivate;
    }else{
      return 0;
    }
  }

  private function get_todays_closed_tickets(){
    $tickets=$this->db_readonly->select("count(id) as total_tickets_closed_today")
    ->from("ticketing_master")
    ->where("date(created_on)",date('Y-m-d'))
    ->where("status","Closed")
    ->get()->row();

    if(!empty($tickets)){
      return $tickets->total_tickets_closed_today;
    }else{
      return 0;
    }
	}

  public function get_ticket_summary_count () {
		// $all_tickets = $this->db->select("count(*) as all_tickets")
		// 	->from('ticketing_master')
		// 	->get()->row();

		$all_tickets = count($this->parent_ticketing_model->get_all_tickets());

    // $open_tickets = $this->db->select("count(*) as open_tickets")
		// 	->from('ticketing_master')
		// 	->where('status', 'open')
		// 	->get()->row();

		$open_tickets = count($this->parent_ticketing_model->get_open_tickets());

    // $closed_tickets = $this->db->select("count(*) as closed_tickets")
    // ->from('ticketing_master')
    // ->where('status', 'Closed')
    // ->where("date(created_on) = curdate()")
    // ->get()->row();

    $closed_tickets= $this->get_todays_closed_tickets();

		// $assigned = $this->db->select("count(*) as assigned")
    // ->from('ticketing_master')
		// 	->where('assigned_to', $this->authorization->getAvatarStakeHolderId())
		// 	->get()->row();

    $staff_id = $this->authorization->getAvatarStakeHolderId();
    $assigned = count($this->parent_ticketing_model->get_assigned_tickets_by_id($staff_id));
      
    $counts = new stdClass();
		$counts->all = $all_tickets;
		$counts->open_tickets = $open_tickets;
    $counts->closed_tickets = $closed_tickets;
		$counts->assigned = $assigned;
    
		return $counts;
	}

  public function get_internal_ticket_summary_count () {
		$all_tickets = $this->db->select("count(*) as all_tickets")
			->from('internal_ticketing_items')
			->get()->row();

		$open_tickets = $this->db->select("count(*) as open_tickets")
			->from('internal_ticketing_items')
			->where_in('ticket_status', ['Open','In Progress','Reopen'])
			->get()->row();

    $resolved_tickets = $this->db->select("count(*) as resolved_tickets")
    ->from('internal_ticketing_items')
    ->where('ticket_status', 'Resolved')
    ->where("date(created_on) = curdate()")
    ->get()->row();

		$assigned = $this->db->select("count(*) as assigned")
			->from('internal_ticketing_items')
      ->where_in("ticket_status",['Open','In Progress','Reopen'])
			->where('assigned_to', $this->authorization->getAvatarStakeHolderId())
			->get()->row();

		$counts = new stdClass();
		$counts->all = $all_tickets->all_tickets;
		$counts->open_tickets = $open_tickets->open_tickets;
    $counts->closed_tickets = $resolved_tickets->resolved_tickets;
		$counts->assigned = $assigned->assigned;

		return $counts;
	}

  public function getClassSection() {
    $sections = $this->db_readonly->select("cs.id as section_id, cs.section_name, cs.class_id, cs.class_name, count(sy.id) as std_count")
    ->from('class_section cs')
    ->join('class c', 'c.id=cs.class_id')
    ->join('student_year sy', 'sy.class_section_id=cs.id')
    ->join('student_admission sa', 'sa.id=sy.student_admission_id')
    ->where('c.is_placeholder', 0)
    ->where('cs.is_placeholder', 0)
    ->where('c.acad_year_id', $this->yearId)
    ->where("sy.promotion_status!='4' and sy.promotion_status!='5' and sa.admission_status=2 and sy.promotion_status!='JOINED'")
    ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
    ->order_by('cs.display_order, c.id, cs.section_name')
    ->group_by('cs.id')
    ->get()->result();
    $class_sections = array();
    foreach ($sections as $key => $sec) {
      if(!array_key_exists($sec->class_id, $class_sections)) {
        $class_sections[$sec->class_id] = array();
        $class_sections[$sec->class_id]['id'] = $sec->class_id;
        $class_sections[$sec->class_id]['name'] = $sec->class_name;
        $class_sections[$sec->class_id]['sections'] = array();
      }
      $class_sections[$sec->class_id]['sections'][] = array('id' => $sec->section_id, 'name' => $sec->section_name, 'std_count' => $sec->std_count);
    }
    $data = array();
    foreach ($class_sections as $sec) {
      $data[] = $sec;
    }
    return $data;
  }

  public function getAttendanceSummary($date) {
    $sql = "SELECT max(m.id), m.class_section_id, SUM(case when std.status=1 then 1 else 0 end) as present, SUM(case when std.status=2 then 1 else 0 end) as absent, count(std.id) as total_students 
            from attendance_v2_student std 
            join attendance_v2_master m ON m.id=std.attendance_v2_master_id 
            where m.id in (select max(id) from attendance_v2_master where date='$date' group by class_section_id) 
            group by std.attendance_v2_master_id";
    $result = $this->db_readonly->query($sql)->result();
    $data = array();
    foreach ($result as $res) {
      $data[$res->class_section_id] = $res;
    }
    return $data;
  }

  public function update_last_access_date(){
    $this->db->where('id',$this->ion_auth->get_user_id());
    return $this->db->update('users',array('last_accessed_on'=>$this->Kolkata_datetime()));
  }


  public function enquiry_day_wise_total_count($from_date, $to_date){
    $yearId = $this->acad_year->getAcadYearID();
    $this->db_readonly->select("count(en.id) as total_enquiry, date_format(en.created_on,'%d-%b') as created_on")
    ->from('enquiry en')
    ->where('en.academic_year',$this->yearId)
    ->group_by("date_format(en.created_on,'%d-%m-%Y')")
    ->order_by('en.created_on','desc');
    $this->db_readonly->where('date_format(en.created_on,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"');

    $dateArry = array();         
    $toDate = strtotime($to_date); 
    $fromDate = strtotime($from_date);
    for ($currentDate = $toDate; $currentDate <= $fromDate; $currentDate += (86400)) {                         
      $Store = date('d-M', $currentDate); 
      $dateArry[$Store] = array('total_enquiry'=>"0",'created_on'=>$Store,); 
    }
    $transCount = $this->db_readonly->get()->result();
    foreach ($transCount as $key => $val) {
      if (array_key_exists($val->created_on, $dateArry)) {
        $dateArry[$val->created_on] = $val;
      }
    }
    return $dateArry;

   
    // return $this->db_readonly->query("select count(id) as enquiries, DATE_FORMAT(created_on,'%d-%b') as week, DATE_FORMAT(created_on,'%d-%b-%y') as weekYear from enquiry where academic_year = $yearId group by WEEK(created_on) order by created_on,'desc'")->result();
  }

  public function get_approval_widget_data(){
    //Get the Staff Leave approval pending count if he is a admin
    $is_leave_admin = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
    $staff_id = $this->authorization->getAvatarStakeHolderId();

    if ($is_leave_admin){
      $st_count =  $this->db->select("count(*) as 'staff_leave_pending_count'")
      ->from('leave_v2_staff')
      ->where('status','0')
      ->get()->row();
      $pending_approval_leave_staff_count = $st_count->staff_leave_pending_count; 
    }
    else{
      //Get the Staff Leave approval pending count if he is a approver
      $is_leave_approver = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE');
      if($is_leave_approver){
        $sql = "SELECT COUNT(*) as staff_leave_pending_count FROM leave_v2_staff WHERE status = 0 AND staff_id IN (SELECT id FROM staff_master WHERE reporting_manager_id = $staff_id)";
        $query = $this->db->query($sql);
        $pending_approval_leave_staff_count = $query->row()->staff_leave_pending_count;
      }
      else{
        $pending_approval_leave_staff_count = 0;
      }
    }

    //getting pending regularize Attendance

    $isAttAdmin=$this->authorization->isAuthorized('STAFF_ATTENDANCE.ADMIN');
    if($isAttAdmin){
      $st_count =  $this->db->select("count(*) as 'pending_regularize_count'")
      ->from('leave_v2_staff_regularize_leave')
      ->where('leave_resolved','0')
      ->get()->row();
      $pending_regularize_leave_staff_count = $st_count->pending_regularize_count; 
    }else{
        $sql="select count(*) as pending_regularize_count
        from leave_v2_staff_regularize_leave srl
        join staff_master sm on sm.id=srl.staff_id
        where leave_resolved=0 and sm.reporting_manager_id=$staff_id";
        $query = $this->db->query($sql);
        $pending_regularize_leave_staff_count = $query->row()->pending_regularize_count;
    }

    //Get the pending approvals for circular
    $is_circular_approver = $this->authorization->isAuthorized('CIRCULARV2.APPROVER');
    if ($is_circular_approver) {
      $circular_query = $this->db->select("count(*) as 'circular_count'")
        ->from('circularv2_master cm')
        ->where('is_approved','0')
        ->get()->row();
      $pending_approval_circular_count = $circular_query->circular_count;
    } else {
      $pending_approval_circular_count = 0;
    }

    //Get the pending approval of all staff's new venture like staff_awards, staff_qualifications, etc
    $is_staff_approver_for_all = $this->authorization->isAuthorized('STAFF.APPROVE_ATTRIBUTE_UPDATES');
    if($is_staff_approver_for_all){
      $sd1=$this->db->select("count(*) 'sd1'")
      ->from('staff_interest')
      ->where('approved_status','Pending')
      ->get()->row();
      $sd2 = $this->db->select("count(*) 'sd2'")
      ->from('staff_awards')
      ->where('approved_status','Pending')
      ->get()->row();
      $sd3=$this->db->select("count(*) 'sd3'")
      ->from('staff_documents')
      ->where('approved_status','Pending')
      ->get()->row();
      $sd4=$this->db->select("count(*) 'sd4'")
      ->from('staff_experience')
      ->where('approved_status','Pending')
      ->get()->row();
      $sd5=$this->db->select("count(*) 'sd5'")
      ->from('staff_qualification_v2')
      ->where('approved_status','Pending')
      ->get()->row();
      $sd6=$this->db->select("count(*) 'sd6'")
      ->from('staff_training_workshop')
      ->where('approved_status','Pending')
      ->get()->row();
      $sd7=$this->db->select("count(*) 'sd7'")
      ->from('staff_initiative_master')
      ->where('approved_status','Pending')
      ->get()->row();
      $sd8=$this->db->select("count(*) 'sd8'")
      ->from('staff_publications_citations')
      ->where('approved_status','Pending')
      ->get()->row();
      $staffd_count=$sd1->sd1+$sd2->sd2+$sd3->sd3+$sd4->sd4+$sd5->sd5+$sd6->sd6+$sd7->sd7+$sd8->sd8;
    }
    else{
      $staffd_count=0;
    }

    //Get the pending approval of student leave
    $is_student_leave_approver = $this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_APPROVE');
    if($is_student_leave_approver){
      $stu_leaves=$this->db->select("count(*) 'st_count'")
      ->from('leave_student')
      ->where('status','Pending')
      ->get()->row();
      $pending_approval_leave_student_count = $stu_leaves->st_count;
    }
    else{
      $pending_approval_leave_student_count = 0;
    }
    
    $result=array(
      'pending_approval_leave_staff_count'=>$pending_approval_leave_staff_count,
      'pending_approval_circular_count'=>$pending_approval_circular_count,
      'pending_approval_leave_student_count'=>$pending_approval_leave_student_count,
      'staffd_count' => $staffd_count,
      'pending_regularize_leave_staff_count'=>$pending_regularize_leave_staff_count
    );
    return $result;
  }

  public function getAllBoards(){
		$result = $this->db->select('id, board_name')
				->from('stb_boards')
				->where('created_by', $this->authorization->getAvatarStakeHolderId())
				->get()
				->result();

		return $result;
	}

  // public function get_task_current_month_calendar(){
  //   $data = [];
    // $data['total_tasks'] = $this->db_readonly->select('COUNT(*) as total_task_count')
		// 										->from('stb_mytasks')
		// 										->where('created_by', $this->authorization->getAvatarStakeHolderId())
		// 										->where('status', 1)
		// 										->where('is_deleted', 0)
    //                     ->where('delegated_to_project is NULL')
		// 										->get()
		// 										->row()
    //                     ->total_task_count;
    
  //   // $data['total_tasks_query'] = $this->db_readonly->last_query();

	// 	$data['delegated_task_count'] = $this->db_readonly->select('COUNT(delegated_to_project) as delegated_task_count')
	// 											->from('stb_mytasks')
	// 											->where('delegated_to_project is NOT NULL')
	// 											->where('created_by', $this->authorization->getAvatarStakeHolderId())
	// 											->where('status', 1)
	// 											->where('is_deleted', 0)
	// 											->get()
	// 											->row()
  //                       ->delegated_task_count;

  //   // $data['delegated_tasks_query'] = $this->db_readonly->last_query();

  //   $today = date("Y-m-d");
    
	// 	$data['due_tasks'] = $this->db_readonly->select('COUNT(due_date) as due_date_count')
	// 											->from('stb_mytasks')
  //                       ->where("DATE_FORMAT(due_date,'%Y-%m-%d') = '$today'")
	// 											->where('created_by', $this->authorization->getAvatarStakeHolderId())
	// 											->where('status', 1)
	// 											->where('is_deleted', 0)
	// 											->get()
	// 											->row()
  //                       ->due_date_count;

  //   // $data['due_tasks_query'] = $this->db_readonly->last_query();

	// 	$data['high_priority_tasks'] = $this->db_readonly->select('COUNT(priority) as h_priority')
	// 											->from('stb_mytasks')
	// 											->where('priority', 1)
	// 											->where('created_by', $this->authorization->getAvatarStakeHolderId())
	// 											->where('status', 1)
	// 											->where('is_deleted', 0)
	// 											->get()
	// 											->row()
  //                       ->h_priority;

  //   // $data['h_priority_tasks_query'] = $this->db_readonly->last_query();
    
  //   $data['total_tasks'] = $data['total_tasks'] ? $data['total_tasks'] : 0;
  //   $data['delegated_task_count'] = $data['delegated_task_count'] ? $data['delegated_task_count'] : 0;
  //   $data['due_tasks'] = $data['due_tasks'] ? $data['due_tasks'] : 0;
  //   $data['high_priority_tasks'] = $data['high_priority_tasks'] ? $data['high_priority_tasks'] : 0;

  //   // echo '<pre>';print_r($data);die();

  //   return $data;
  // }

  public function getallopenmytasks($list_id) {
      $result = $this->db_readonly->select('COUNT(*) as total_task_count')
                                  ->from('stb_mytasks')
                                  ->where('created_by', $this->authorization->getAvatarStakeHolderId())
                                  ->where('status', 1)
                                  ->where('is_deleted', 0)
                                  ->where('delegated_to_project IS NULL')
                                  ->where('stb_mytasklist_id', $list_id)
                                  ->get()
                                  ->row()
                                  ->total_task_count;

      return $result;
  }

  public function getalldelegatedtasks($list_id) {
      $result = $this->db_readonly->select('COUNT(delegated_to_project) as delegated_task_count')
												->from('stb_mytasks')
												->where('delegated_to_project is NOT NULL')
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->delegated_task_count;

      return $result;
  }

  public function getallduetasks($list_id) {
      $today = date("Y-m-d"); 
      $due_date_count = $this->db_readonly->select('COUNT(due_date) as due_date_count')
												->from('stb_mytasks')
                        ->where('delegated_to_project IS NULL')
                        ->where("DATE_FORMAT(due_date,'%Y-%m-%d') = '$today'")
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->due_date_count;

      $h_priority_count = $this->db_readonly->select('COUNT(priority) as h_priority')
												->from('stb_mytasks')
                        ->where('delegated_to_project IS NULL')
												->where('priority', 1)
                        ->where("DATE_FORMAT(due_date,'%Y-%m-%d') = '$today'")
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->h_priority;
      return ['due_date_count' => $due_date_count, 'h_priority_count' => $h_priority_count];
  }

  public function getallhighprioritytasks($list_id) {
      $result = $this->db_readonly->select('COUNT(priority) as h_priority')
												->from('stb_mytasks')
                        ->where('delegated_to_project IS NULL')
												->where('priority', 1)
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->h_priority;

      return $result;
  }

  public function get_staff_attendance_current_month_calendar($data){
    $staff_id=$this->authorization->getAvatarStakeHolderId();
    // $staff_id=50;

    $current_year_month="DATE_FORMAT(CURRENT_DATE(), '%Y-%m')";
    return $this->db_readonly->query("select sta.is_late,sta.id,sta.date, sta.status,date_format(sta.date,'%d') as created_on from st_attendance sta where staff_id=$staff_id and date_format(date,'%Y-%m')=$current_year_month")->result();
  }

  public function save_dashboard_order($dashboard_order, $avatar_id) {
		$this->db->where('id', $avatar_id);
		return $this->db->update('avatar',array('dashboard_order'=>$dashboard_order));
  }

  public function get_staff_leave_quota_usage($staff_id, $leave_v2_year_id=0) {
    if(!$leave_v2_year_id) {
      $result = $this->db->select('id')->where('is_active', 1)->get('leave_v2_year')->row();
      if ($result) {
        $leave_v2_year_id = $result->id;
      } else {
        return '';
      }
    }
    $result = $this->db_readonly->select("lsq.id, lsq.staff_id, lsq.leave_category_id, lsq.total_quota, lsq.used_quota, lsq.quota_carried_to_next_year, lc.name, lc.short_name, lc.leave_type, lc.has_quota, lc.can_be_carried")->from('leave_v2_staff_quota lsq')->join('leave_v2_category lc', 'lc.id=lsq.leave_category_id')->where('lsq.leave_v2_year_id', $leave_v2_year_id)->where('lsq.staff_id', $staff_id)->get()->result();

    $categories = $this->db_readonly->select('name, short_name as lc_short_name, id as lc_id, 0 as total_quota, 0 as used_quota')
      ->from('leave_v2_category')
    ->get()->result();

    foreach ($categories as &$cat) {
      foreach ($result as $res) {
        if ($cat->lc_id == $res->leave_category_id) {
          $cat->total_quota = $res->total_quota;
          $cat->used_quota = $res->used_quota;
          break;
        }
      }
    }

    return $categories;
  }

  public function get_enquiry_statistics() {
    $yearId = $this->acad_year->getAcadYearID();
    $data = array();
    $today = date('Y-m-d');

    $date = new DateTime("now");

    $curr_date = $date->format('Y-m-d ');

    $data['unassigned'] = $this->db_readonly->select("count(e.id) as unassigned")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','trim(es.user_status) = trim(e.status)')
    ->where('e.academic_year',$yearId)
    ->where('(e.assigned_to is null OR e.assigned_to = 0)')
    ->where('es.reporting_status !=','invalid')
    ->get()->row()->unassigned;

    $monday = $today;
    if(date('D') != 'Mon') {
      $monday = date('Y-m-d',strtotime("last monday"));
    }
    $data['enquiries_today'] = $this->db_readonly->select("count(e.id) as enquiries_today")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("DATE_FORMAT(e.created_on, '%Y-%m-%d')='$today'")
    ->get()->row()->enquiries_today;

    $data['enquiries_this_week'] = $this->db_readonly->select("count(e.id) as this_week")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("(DATE_FORMAT(e.created_on, '%Y-%m-%d')>='$monday' and DATE_FORMAT(e.created_on, '%Y-%m-%d')<='$today')")
    ->get()->row()->this_week;

    $data['total_enquiries'] = $this->db_readonly->select('count(id) as total')->where('academic_year',$yearId)->get('enquiry')->row()->total;

    $active_data = $this->db_readonly->select("count(e.id) as active")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','es.user_status = e.status')
    ->where('e.academic_year',$yearId)
    ->where_in('es.reporting_status','wip')
    ->get()->row();

    $data['active_enquiries'] = $active_data->active;

    $pending = $this->db_readonly->select("count(e.id) as pending_followups")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','es.user_status = e.status')
    ->where('e.academic_year',$yearId)
    ->where("e.status = 'Follow-up required'")
    ->get()->row();
   
    $data['pending_followups'] = $pending->pending_followups;

    $overdue = $this->db_readonly->select("count(e.id) as overdeu_followup")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("e.status = 'Follow-up required'")
    ->where("DATE_FORMAT(e.next_follow_date, '%Y-%m-%d')<='$today'")
    ->get()->row();
   
    $data['overdeu_followup'] = $overdue->overdeu_followup;

    $processed_data = $this->db_readonly->select("count(e.id) as processed_count")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','es.user_status = e.status')
    ->where('e.academic_year',$yearId)
    ->where_in('es.reporting_status','convert')
    ->get()->row();

    $data['processed'] = $processed_data->processed_count;

    $data['activity_today'] = $this->db_readonly->select('count(fu.id) as activity_today')
    ->from('follow_up fu')
    ->where('fu.follow_up_type','Enquiry')
    ->where('date_format(fu.created_on,"%Y-%m-%d")',$today)
    ->where("fu.source_id in (select id from enquiry e where e.academic_year=$yearId)")
    ->get()->row()->activity_today;

    return $data;
   // echo "<pre>"; print_r($data); die();
  }

  public function get_substitution_statistics(){
    $today = date('Y-m-d');
    $leave_staff = "select count(*) as staff_on_leave_count from ttv2_substitution_input si
                            JOIN ttv2_substitution_day sd on sd.id = si.ttv2_sub_day_id
                            where si.source_type = 'Leave'
                            and sd.sub_date = '$today'";
    $data['leave_staff'] = $this->db_readonly->query($leave_staff)->row()->staff_on_leave_count;

    $add_hoc_staff = "select count(*) as add_hoc_staff_count from ttv2_substitution_input si
    JOIN ttv2_substitution_day sd on sd.id = si.ttv2_sub_day_id
    where si.source_type = 'Ad-hoc addition'
    and sd.sub_date = '$today'";
    $data['add_hoc_staff'] = $this->db_readonly->query($add_hoc_staff)->row()->add_hoc_staff_count;

    $template_id = $this->db_readonly->select('id')->from('ttv2_template')->where('acad_year_id', $this->yearId)->where('status', 'active')->get()->row();
    if($template_id){
      $total_substitution = "select count(distinct(old_ttv2_staff_period_id)) as total_substitution
      from ttv2_substitution_output so
      JOIN ttv2_substitution_input si on si.id = so.ttv2_sub_input_id
      JOIN ttv2_substitution_day sd on sd.id = si.ttv2_sub_day_id
      where sd.sub_date = '$today' and ttv2_template_id = $template_id->id";
      $data['total_substitution'] = $this->db_readonly->query($total_substitution)->row()->total_substitution;
  
      $pending_substitutions = "select count(distinct(old_ttv2_staff_period_id)) as pending_substitutions
      from ttv2_substitution_output so
      JOIN ttv2_substitution_input si on si.id = so.ttv2_sub_input_id
      JOIN ttv2_substitution_day sd on sd.id = si.ttv2_sub_day_id
      where sd.sub_date = '$today' and ttv2_template_id = $template_id->id
      and so.sub_staff_id is null"; 
      $data['pending_substitutions'] = $this->db_readonly->query($pending_substitutions)->row()->pending_substitutions;
    }
    else{
      $data['total_substitution'] = 0;
      $data['pending_substitutions'] = 0;
    }
    return $data;
  }

  public function get_task_basket_data(){
    $data = [];
    $data['total_tasks'] = $this->db_readonly->select('COUNT(*) as total_task_count')
												->from('stb_mytasks')
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        ->where('delegated_to_project is NULL')
                        // ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->total_task_count;
    
    // $data['total_tasks_query'] = $this->db_readonly->last_query();

		$data['delegated_task_count'] = $this->db_readonly->select('COUNT(delegated_to_project) as delegated_task_count')
												->from('stb_mytasks')
												->where('delegated_to_project is NOT NULL')
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        // ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->delegated_task_count;

    // $data['delegated_tasks_query'] = $this->db_readonly->last_query();

    $today = date("Y-m-d");
    
		$data['due_tasks'] = $this->db_readonly->select('COUNT(due_date) as due_date_count')
												->from('stb_mytasks')
                        ->where("DATE_FORMAT(due_date,'%Y-%m-%d') = '$today'")
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        // ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->due_date_count;

    // $data['due_tasks_query'] = $this->db_readonly->last_query();

		$data['high_priority_tasks'] = $this->db_readonly->select('COUNT(priority) as h_priority')
												->from('stb_mytasks')
												->where('priority', 1)
												->where('created_by', $this->authorization->getAvatarStakeHolderId())
												->where('status', 1)
												->where('is_deleted', 0)
                        // ->where('stb_mytasklist_id', $list_id)
												->get()
												->row()
                        ->h_priority;

    // $data['h_priority_tasks_query'] = $this->db_readonly->last_query();
    
    $data['total_tasks'] = $data['total_tasks'] ? $data['total_tasks'] : 0;
    $data['delegated_task_count'] = $data['delegated_task_count'] ? $data['delegated_task_count'] : 0;
    $data['due_tasks'] = $data['due_tasks'] ? $data['due_tasks'] : 0;
    $data['high_priority_tasks'] = $data['high_priority_tasks'] ? $data['high_priority_tasks'] : 0;

    // echo '<pre>';print_r($data);die();

    return $data;

  }

  public function get_visitor_widget_data() {
    $this->db_readonly->select('COUNT(*) as check_in');
    $this->db_readonly->from('visitor_v2_info');
    $this->db_readonly->where("DATE(check_in)",date('Y-m-d'));
    $check_in = $this->db_readonly->get()->row();
   
    $this->db_readonly->select('COUNT(check_out) as check_out');
    $this->db_readonly->from('visitor_v2_info');
    $this->db_readonly->where("DATE(check_out)",date('Y-m-d'));
    $check_out = $this->db_readonly->get()->row();
    
    $result = new stdClass();
    $result->check_in = $check_in->check_in ;
    $result->check_out = $check_out->check_out;
    $result->total_number_of_visitor = $result->check_in + $result->check_out;
    return $result;
  }

  public function get_staff_completed_years(){
    // Get current date
    $today = date('Y-m-d');
    
    // $monday = date('Y-m-d', strtotime('monday this week', strtotime($today)));
    // $sunday = date('Y-m-d', strtotime('sunday this week', strtotime($today)));
    $monday = date('Y-m-d', strtotime('monday last week', strtotime($today)));
    $sunday = date('Y-m-d', strtotime('sunday last week', strtotime($today)));
    $Monday = date('d-m-Y', strtotime('monday this week', strtotime($today)));
    $Sunday = date('d-m-Y', strtotime('sunday this week', strtotime($today)));

    // Extract the month and day from Monday and Sunday
    $monday_month_day = date('m-d', strtotime($monday));
    $sunday_month_day = date('m-d', strtotime($sunday));

    // Query to get staff details where joining date's month and day is between $monday and $sunday
    $this->db_readonly->select("
        CONCAT(IFNULL(first_name, ''), ' ', IFNULL(last_name, '')) AS staff_name,
        employee_code,
        joining_date,
        DATE_FORMAT(DATE_ADD(joining_date, INTERVAL TIMESTAMPDIFF(YEAR, joining_date, '$sunday') YEAR), '%Y-%m-%d') AS completed_on_date,
        TIMESTAMPDIFF(YEAR, joining_date, '$sunday') AS years_completed
    ");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2');
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('NOT ISNULL(joining_date)');
    
    // Only get staff whose joining date month and day are between $monday and $sunday (ignore the year)
    $this->db_readonly->where('DATE_FORMAT(joining_date, "%m-%d") >=', $monday_month_day);
    $this->db_readonly->where('DATE_FORMAT(joining_date, "%m-%d") <=', $sunday_month_day);
    $this->db_readonly->order_by('years_completed', 'ASC');
    $this->db_readonly->order_by('first_name', 'ASC');
    $result = $this->db_readonly->get()->result();

    // Filter to return data for staff who have completed at least 1 year
    $final_result = [];
    foreach ($result as $staff) {
        if (in_array($staff->years_completed, [1, 5, 10])) {
            $final_result[] = $staff;
        }
    }

    return [
        'week_start' => $Monday,
        'week_end' => $Sunday,
        'anniversary_data' => $final_result
    ];
  }

  public function get_staff_data_for_anniversary()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.
    $is_staff_anniversary_approver = $this->authorization->isAuthorized('WIDGET.STAFF_ANNIVERSARY_WIDGET');
    if($is_staff_anniversary_approver){
      $result = array (
        '6_months_anniversary' => $this->get_staff_anniversary_details_for_6_months(),
        '1_year_anniversary' => $this->get_staff_anniversary_details_for_1_year(),
        '3_year_anniversary' => $this->get_staff_anniversary_details_for_3_years(),
        '5_year_anniversary' => $this->get_staff_anniversary_details_for_5_years(),
        '7_year_anniversary' => $this->get_staff_anniversary_details_for_7_years(),
        '10_year_anniversary' => $this->get_staff_anniversary_details_for_10_years(),
      );
      return $result;
    }
  }

  public function get_staff_anniversary_details_for_6_months()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.
    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(DATE_ADD(joining_date, INTERVAL +6 MONTH),'%M %d') as joining_date, joining_date as date, date_format(joining_date, '%M') as joining_monthDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('not isnull(joining_date)');
    $this->db_readonly->where('(DATE_ADD(joining_date, INTERVAL +6 MONTH) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY))');
    $this->db_readonly->order_by('joining_date');
    $result = $this->db_readonly->get()->result();
    return $result;
  }
  
  public function get_staff_anniversary_details_for_1_year()
  {
    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(joining_date,'%M %d') as joining_date, joining_date as date, date_format(joining_date, '%Y') as joining_yearDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('not isnull(joining_date)');
    // $this->db_readonly->where('DATE_FORMAT(joining_date,"%m-%d") < DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->where('DATE_ADD(joining_date, INTERVAL 1 YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)');
    $this->db_readonly->order_by('DATE_FORMAT(joining_date,"%m-%d")');
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_staff_anniversary_details_for_5_years()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.
    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(joining_date,'%M %d') as joining_date, joining_date as date, date_format(joining_date, '%Y') as joining_yearDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('not isnull(joining_date)');
    // $this->db_readonly->where('DATE_FORMAT(joining_date,"%m-%d") < DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->where('(DATE_ADD(joining_date, INTERVAL 5 YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY))');
    $this->db_readonly->order_by('DATE_FORMAT(joining_date,"%m-%d")');
    $result = $this->db_readonly->get()->result();
    return $result;
  }
  public function get_staff_anniversary_details_for_3_years()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.
    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(joining_date,'%M %d') as joining_date, joining_date as date, date_format(joining_date, '%Y') as joining_yearDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('not isnull(joining_date)');
    // $this->db_readonly->where('DATE_FORMAT(joining_date,"%m-%d") < DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->where('(DATE_ADD(joining_date, INTERVAL 3 YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY))');
    $this->db_readonly->order_by('DATE_FORMAT(joining_date,"%m-%d")');
    $result = $this->db_readonly->get()->result();
    return $result;
  }
  public function get_staff_anniversary_details_for_7_years()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.
    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(joining_date,'%M %d') as joining_date, joining_date as date, date_format(joining_date, '%Y') as joining_yearDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('not isnull(joining_date)');
    // $this->db_readonly->where('DATE_FORMAT(joining_date,"%m-%d") < DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->where('(DATE_ADD(joining_date, INTERVAL 7 YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY))');
    $this->db_readonly->order_by('DATE_FORMAT(joining_date,"%m-%d")');
    $result = $this->db_readonly->get()->result();
    return $result;
  }
  public function get_staff_anniversary_details_for_10_years()
  {
    //TODO: This code breaks for December. It does not cycle through the next month i.e. January. Need to fix.
    $this->db_readonly->select("id, gender, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name, date_format(joining_date,'%M %d') as joining_date, joining_date as date, date_format(joining_date, '%Y') as joining_yearDisplay");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('status', '2'); //'2' => 'Approved'
    $this->db_readonly->where('is_primary_instance', 1);
    $this->db_readonly->where('not isnull(joining_date)');
    // $this->db_readonly->where('DATE_FORMAT(joining_date,"%m-%d") < DATE_FORMAT(CURRENT_DATE,"%m-%d")');
    $this->db_readonly->where('(DATE_ADD(joining_date, INTERVAL 10 YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY))');
    $this->db_readonly->order_by('DATE_FORMAT(joining_date,"%m-%d")');
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_transport_statistics_data(){
      $total_buses =  $this->db_readonly->select("count(id) as total_count")
      ->from('tx_things tx')
      ->get()->row();

      $journeys =  $this->db_readonly->select("count(id) as total_journeys")
      ->from('tx_journeys tj')
      ->where('status',1)
      ->get()->row();
      
      $student_count =  $this->db_readonly->select("count(distinct(entity_source_id)) as student_count")
      ->from('tx_student_journeys tsj')
      ->join('student_admission sa','tsj.entity_source_id = sa.id')
      ->join('student_year sy','sa.id = sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->get()->row();

      $staff_count =  $this->db_readonly->select("count(distinct(staff_id)) as staff_count")
      ->from('tx_staff_journeys tsj')
      ->join('staff_master sm','tsj.staff_id = sm.id')
      ->where('status', 2)
      ->get()->row();

      $resArry = array(
        'total_buses'=>$total_buses->total_count,
        'journeys'=>$journeys->total_journeys,
        'student_count'=>$student_count->student_count,
        'staff_count'=>$staff_count->staff_count
      );
      return $resArry;
  }

  public function get_ids_from_rfid($rfid_number){
    $result = [];
    $query = "select student_id from parent where rfid_number = '$rfid_number'";
    $parent = $this->db_readonly->query($query)->result();
    if($parent){
      foreach ($parent as $stu_ids){
        $this->db_readonly->select("sd.id as id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name, cs.section_name, cs.id as class_section_id, c.class_name, sd.rfid_number ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss',"sd.id=ss.student_admission_id and ss.acad_year_id=$this->yearId");
        $this->db_readonly->where('sd.id',$stu_ids->student_id);
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        $res = $this->db_readonly->get()->result();
        array_push($result, $res);
      }
      return $result;
    }
    else{
      $this->db_readonly->select("sd.id as id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name, cs.section_name, cs.id as class_section_id, c.class_name, sd.rfid_number ");
      $this->db_readonly->from('student_admission sd');
      $this->db_readonly->join('student_year ss',"sd.id=ss.student_admission_id and ss.acad_year_id=$this->yearId");
      $this->db_readonly->where('sd.rfid_number',$rfid_number);
      $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
      $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
      $res = $this->db_readonly->get()->result();
      array_push($result, $res);
      return $result;
    }
  }

  public function get_counselling_statistics() {
    $yearId = $this->acad_year->getAcadYearID();
    $data = array();

    $today = date('Y-m-d');
    $date = new DateTime("now");
    $curr_date = $date->format('Y-m-d ');

    $monday = $today;
    if(date('D') != 'Mon') {
      $monday = date('Y-m-d',strtotime("last monday"));
    }

    //counselling this week
    $data['counselling_this_week'] = $this->db_readonly->select("count(e.id) as this_week")
    ->from('student_counselling e')
    ->where("(DATE_FORMAT(e.counselling_date, '%Y-%m-%d')>='$monday' and DATE_FORMAT(e.counselling_date, '%Y-%m-%d')<='$today')")
    ->get()->row()->this_week;

    //total counselling
    $data['total_counselling'] = $this->db_readonly->select('count(id) as total')->get('student_counselling')->row()->total;

    //Activity today
    $data['activity_today'] = $this->db_readonly->select('count(sc.id) as activity_today')
    ->from('student_counselling sc')
    ->where('date_format(sc.counselling_date,"%Y-%m-%d")',$today)
    ->get()->row()->activity_today;

    return $data;
  }

  public function get_nc_statistics(){
    $today = date('Y-m-d');
    $sql = "SELECT count(*) as count, sc.name
    FROM snc_items si
    JOIN snc_category sc ON sc.id = si.snc_category_id
    where date(si.created_on) = '$today'
    group by sc.name";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function get_library_statistics_data(){
    $total_books =  $this->db_readonly->select("count(lbc.id) as total_count")
    ->from('library_books lb')
    ->where('lbc.status','Available')
    ->join('library_books_copies lbc','lb.id=lbc.book_id')
    ->get()->row();

    $noof_cards = $this->db_readonly->select('count(id) as total_count')
    ->from('library_cards')
    ->get()->row();

    $transactions = $this->db_readonly->select('count(id) as total_count')
    ->from('library_transacation')
    ->where('status',1)
    ->get()->row();
    $trnsCount = 0;
    if(!empty($transactions)){
      $trnsCount = $transactions->total_count;
    }

    $library_cards = 0;
    if(!empty($noof_cards)){
      $library_cards = $noof_cards->total_count;
    }


    $trans_overdue_date = $this->db->select("lt.book_access_id, date_format(lt.issue_date,'%d-%m-%Y') as issue_date,lm.hold_period")
    ->from('library_transacation lt')
    ->where('lt.status','1')
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->get()->result();

    $overdue_booksId = array();
    foreach ($trans_overdue_date as $key => $val) {
      $issueDate = $val->issue_date;
      $cDate = date('Y-m-d');
      $dayCount = $val->hold_period;
      // $due_date[$val->transId] = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
      $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
      if ($cDate > $due_date) {          
        array_push($overdue_booksId, $val->book_access_id);
      } 
    }

    if (!empty($overdue_booksId)) {
    $overDue = $this->db->select('count(book_id) as overDue')
        ->from('library_transacation lt')
        ->join('library_books_copies lbc', 'lt.book_access_id=lbc.id')
        ->join('library_books lb', 'lbc.book_id=lb.id')
        ->where_in('lt.book_access_id', $overdue_booksId)
        ->where('lt.status', '1')
        ->get()->result();

      }

      $overdue_count = 0;
      if (!empty($overDue)) {
          $overdue_count = $overDue[0]->overDue;
      } 

      $total_books->circulation = $trnsCount;
      $total_books->overdue_count_due = $overdue_count;
      $total_books->no_of_cards = $library_cards;
      return $total_books;
  }

  public function get_staff_checkin_reporting_wise($todays_date){
    $reporting_manager_id = $this->authorization->getAvatarStakeHolderId();

    $staffs_query = "select staff_id, date, shift_type, sh.name as shift_name, CONCAT(sm.first_name, ' ', sm.last_name) AS full_name
    from st_attendance_staff_shifts ss
    join st_attendance_shifts_master sh on sh.id=ss.shift_master_id
    join staff_master sm on sm.id = staff_id and reporting_manager_id = $reporting_manager_id
    where ss.date='$todays_date' and sm.status = 2 AND sm.is_primary_instance = 1
    group by staff_id, ss.date
    order by full_name";
    $staffs_result = $this->db_readonly->query($staffs_query)->result();
    $total_staff = count($staffs_result);
    if($total_staff == 0){
      return 0;
    }

    $check_in_query = "SELECT DISTINCT staff_id, first_check_in_time
    from st_attendance sa
    join staff_master sm on sm.id = sa.staff_id
    where date='$todays_date'
    AND reporting_manager_id = $reporting_manager_id and sm.status = 2
    order by staff_id";
    $check_in_result = $this->db_readonly->query($check_in_query)->result();

    $comparison_result = array();
    $checked_in_count = 0;
    foreach ($staffs_result as $staff) {
      $status = '';
      $is_found=false;
      $leave = $this->check_leave($staff->staff_id, $todays_date);
      if ($leave) {
          $status = 'Leave';
      } 
      else {
        if(count($check_in_result) > 0){
          foreach ($check_in_result as $check_in) {
            if ($staff->staff_id == $check_in->staff_id) {
              if($check_in->first_check_in_time != '' && $check_in->first_check_in_time != 'null'){
                $status = local_time($check_in->first_check_in_time, 'H:i A');
                $checked_in_count += 1;
                $is_found=true;
              }
              else{
                $status = 'Not Checked In';
              }
            }
          }
          if(!$is_found){
            $status = 'Not Checked In';
          }
        }
        else{
          if ($staff->shift_type != 1){
            $status = $staff->shift_name;
          }
          else{
            $status = 'Not Checked In';
          }
        }
        $comparison_entry = new stdClass();
        $comparison_entry->staff_id = $staff->staff_id;
        $comparison_entry->staff_name = $staff->full_name;
        $comparison_entry->first_check_in_time = $status;
        $comparison_result[] = $comparison_entry;
      }
    }
    $ret = array(
        'total_staff' => $total_staff,
        'checked_in' => $checked_in_count,
        'not_checked_in' => $total_staff - $checked_in_count,
        'staffs' => $comparison_result
    );
    return $ret;
  }

  public function check_leave($staff_id, $todays_date){
    $sql = "SELECT id FROM leave_v2_staff
    WHERE staff_id = $staff_id
    AND $todays_date BETWEEN from_date AND to_date
    AND (status = 1 OR status = 2)";
    $result=$this->db_readonly->query($sql)->row();

    if(!empty($result)){
      return $result->id;
    }else{
      return 0;
    }
  }

  public function get_admission_statistics(){
    $today = date('Y-m-d');
        $monday = $today;
        if(date('D') != 'Mon') {
            $monday = date('Y-m-d',strtotime("last monday"));
        }

        $yearId = $this->acad_year->getAcadYearID();

        $duplicateStatus =$this->settings->getSetting('admission_duplicate_count_removed');
        
        $this->db_readonly->select('count(*) as count');
        $this->db_readonly->from('admission_forms af');
        $this->db_readonly->join('admission_status as', 'af.id = as.af_id');
        $this->db_readonly->where('academic_year_applied_for', $yearId);
        $this->db_readonly->where('as.curr_status != "Draft"');
        if($duplicateStatus){
            $this->db_readonly->where('as.curr_status != "Duplicate"');
        }
        $data['total_count'] =$this->db_readonly->get()->row()->count;

        //echo "<pre>"; print_r($data['total_count']); die();


        $data['total_converted'] = $this->db_readonly->select('count(af.id) as count')
        ->from('admission_forms af')
        ->join('admission_status as', 'af.id = as.af_id')
        ->join('admission_internal_status_map asm', 'as.curr_status = asm.user_status')
        ->where('academic_year_applied_for', $yearId)
        ->where('asm.reporting_status', 'convert')
        ->get()->row()->count;
            //echo "<pre>"; print_r($data['total_converted']); die();
        
        $data['total_activity'] = $this->db_readonly->select('count(fu.id) as count')
        ->from('follow_up fu')
        ->join('admission_forms af', 'fu.source_id=af.id')
        ->where('fu.follow_up_type','Admission')
        ->where('academic_year_applied_for', $yearId)
        ->where('date_format(fu.created_on,"%Y-%m-%d")',$today)
        ->get()->row()->count;

        //echo "<pre>"; print_r($data['total_activity']); die();

        $data['weekly_applications'] = $this->db_readonly->select('count(af.id) as count')
        ->from('admission_forms af')
        ->join('admission_status as', 'af.id = as.af_id')
        ->where('academic_year_applied_for', $yearId)
        ->where('as.curr_status != "Draft"')
        ->where("(DATE_FORMAT(af.created_on, '%Y-%m-%d')>='$monday' and DATE_FORMAT(af.created_on, '%Y-%m-%d')<='$today')")
        ->get()->row()->count;

        //echo "<pre>"; print_r($data['week_activity']); die();
        
        $data['today\'s_application'] = $this->db_readonly->select('count(af.id) as count')
        ->from('admission_forms af')
        ->join('admission_status as', 'af.id = as.af_id')
        ->where('academic_year_applied_for', $yearId)
        ->where('as.curr_status != "Draft"')
        ->where_in('af.created_on ', $today)
        ->get()->row()->count;

        $next_follow_ups = $this->db_readonly->select('count(fu.id) as count')
        ->from('follow_up fu')
        ->join('admission_forms af', 'fu.source_id=af.id')
        ->where('fu.follow_up_type','Admission')
        ->where('academic_year_applied_for', $yearId)
        ->where('date_format(fu.next_follow_date,"%Y-%m-%d") >= ',$today)
        ->get()->row();

        //echo "<pre>"; print_r($data['week_activity']); die();

        $data['next_follow_ups'] = 0;
        if(!empty($next_follow_ups)){
            $data['next_follow_ups'] = $next_follow_ups->count;
        }
        return $data;
  }

  public function get_single_window_count(){
    $today = date('Y-m-d');
    $data['total_visitors'] = $this->db_readonly->select('count(distinct(student_id)) as total_count')
    ->from('single_window_approval_tracking')
    ->where('academic_year_id',$this->acad_year->getAcadYearId())
    ->get()->row()->total_count;

    $data['today_count'] = $this->db_readonly->select('count(distinct(student_id)) as today_count')
    ->from('single_window_approval_tracking spt')
    ->where('date_format(taken_on,"%Y-%m-%d") >= ',$today)
    ->where('academic_year_id',$this->acad_year->getAcadYearId())
    ->get()->row()->today_count;

    return $data;
  }

  public function get_single_window_weekly_data(){
    $single_window_date_range = [];
    $default_from_date = date('Y-m-d'); // today's date
    $default_to_date = date('Y-m-d', strtotime('+6 days')); // 6 days from today

    $raw_config = $this->settings->getSetting('single_window_date_range');

    $single_window_date_range = [];

    if (!empty($raw_config)) {
        $single_window_date_range = array_column($raw_config, 'value', 'name'); 
    }

    $from_date = isset($single_window_date_range['from_date']) ? $single_window_date_range['from_date'] : $default_from_date;
    $to_date   = isset($single_window_date_range['to_date']) ? $single_window_date_range['to_date'] : $default_to_date;

    $new_adm_counts = $this->db_readonly->select("date_format(spt.taken_on,'%d-%b') as taken_on, count(distinct(spt.student_id)) as new_adm_count")
      ->from('single_window_approval_tracking spt')
      ->join('student_admission sa','spt.student_id=sa.id')
      ->join('student_year sy','sy.student_admission_id=sa.id and spt.academic_year_id=sy.acad_year_id')
      ->where("DATE_FORMAT(taken_on,'%Y-%m-%d') >=", $from_date)
      ->where("DATE_FORMAT(taken_on,'%Y-%m-%d') <=", $to_date)
      ->where('spt.academic_year_id',$this->acad_year->getAcadYearId())
      ->where('sy.admission_type',2)
      ->group_by("date_format(taken_on,'%d-%m-%Y')")
      ->order_by('taken_on','desc')
      ->get()->result();
      // echo '<pre>';print_r($new_adm_counts);
      $re_adm_counts = $this->db_readonly->select("DATE_FORMAT(spt.taken_on,'%d-%b') AS taken_on, COUNT(DISTINCT spt.student_id) AS re_adm_count")
      ->from('single_window_approval_tracking spt')
      ->join('student_admission sa', 'spt.student_id = sa.id')
      ->join('student_year sy', 'sy.student_admission_id = sa.id AND spt.academic_year_id = sy.acad_year_id')
      ->where("DATE_FORMAT(taken_on,'%Y-%m-%d') >=", $from_date)
      ->where("DATE_FORMAT(taken_on,'%Y-%m-%d') <=", $to_date)
      ->where('spt.academic_year_id', $this->acad_year->getAcadYearId())
      ->where('sy.admission_type', 1)
      ->group_by("DATE_FORMAT(taken_on,'%d-%m-%Y')")
      ->order_by('taken_on', 'desc')
      ->get()
      ->result();

      $all_count = $this->db_readonly->select("date_format(spt.taken_on,'%d-%b') as taken_on, count(distinct(spt.student_id)) as all_count")
      ->from('single_window_approval_tracking spt')
      ->where('spt.academic_year_id',$this->acad_year->getAcadYearId())
      ->group_by("date_format(taken_on,'%d-%m-%Y')")
      ->order_by('taken_on','desc')
      ->get()->result();
      // echo '<pre>';print_r($re_adm_counts);die();

    //Initialize the date array
    $date_arr = array();
    $from_date_str = strtotime($from_date);
    $to_date_str = strtotime($to_date);
    for ($temp_date = $from_date_str; $temp_date <= $to_date_str; $temp_date += 86400) {
        $temp_date_mod = date('d-M', $temp_date);
        $date_arr[$temp_date_mod] = array(
            'new_adm_count' => "0",
            're_adm_count'  => "0"
        );
    }
    foreach ($new_adm_counts as $val1) {
      $key = date('d-M', strtotime($val1->taken_on));
      if (array_key_exists($key, $date_arr)) {
          $date_arr[$key]['new_adm_count'] = $val1->new_adm_count;
      }
    }
    
    foreach ($re_adm_counts as $val2) {
        $key = date('d-M', strtotime($val2->taken_on));
        if (array_key_exists($key, $date_arr)) {
            $date_arr[$key]['re_adm_count'] = $val2->re_adm_count;
        }
    }
    
    foreach ($all_count as $val3) {
        $key = date('d-M', strtotime($val3->taken_on));
        if (array_key_exists($key, $date_arr)) {
            $date_arr[$key]['all_count'] = $val3->all_count;
        }
    }
    // echo '<pre>';print_r($date_arr);die();
    return $date_arr;
  }
    // public function get_bus_url($thingId) {
    //     $url = $this->db_readonly
    //                 ->select("CASE WHEN tracking_url = '' THEN '-' ELSE tracking_url END as url")
    //                 ->from('tx_things')
    //                 ->where('id', $thingId)
    //                 ->get()
    //                 ->row()
    //                 ->url;

    //     return $url;
    // }

    public function get_activeSalesYear() {
      $sales_year= $this->db_readonly->where('is_active', 1)->get('procurement_sales_year')->row();
      $activeSalesYear= 0;
      if(!empty($sales_year)) {
        $activeSalesYear= $sales_year->id;
      }

      return $activeSalesYear;
    }
    public function getInventoryStatistics($activeSalesYear, $force_refresh = false) {
       // Cache copied from Shrawan's code
       $this->load->driver('cache', array('adapter' => 'file'));
       $school_sub_domain = CONFIG_ENV['school_sub_domain'];
 
       if (!empty($school_sub_domain)) {
         $cache_key = $school_sub_domain . "_inventory_statistics_widget";
 
         // Check if the result is already cached
         $result = $this->cache->get($cache_key);
         if ($force_refresh || (!$result || empty($result))) {
             // Cache miss, so query the database
             log_message('error', 'Creating cache ' . $cache_key);
             $result = $this->getInventoryStatisticsFromQuery($activeSalesYear);
             // Save the result to cache
             $half_day = 6 * 3600;
             $this->cache->save($cache_key, $result, $half_day); //half day cache time
         } else {
             // Cache hit, use the cached result
             log_message('error', 'cache hit! ' . $cache_key);
             $result = $this->cache->get($cache_key);
         }
       } else {
           //Do not work with cache if $school_sub_domain is not defined
           $result =$this->getInventoryStatisticsFromQuery($activeSalesYear);
       }
 
       return $result;
    }

    function getInventoryStatisticsFromQuery($activeSalesYear) {
      if($activeSalesYear == 0) {
        $threshold= $this->db_readonly->select("`pii`.`threshold_quantity`,  SUM(pInvIem.current_quantity) as count")
        ->join('procurement_delivery_challan_items pInvIem', 'pInvIem.proc_im_items_id = pii.id')
        ->group_by('pii.id')
        ->get('procurement_itemmaster_items pii')->result();

        $skus= $this->db_readonly->select("count(distinct(id)) as count")->get('procurement_itemmaster_items')->row();
        $vendors= $this->db_readonly->select("count(distinct(id)) as count")->where("vendor_name != 'Initial Quantity'")->where("status", 1)->get('procurement_vendor_master')->row();
        $inventoryQuantities= $this->db_readonly->select("sum(ifnull(initial_quantity, 0)) as count")->get('procurement_delivery_challan_items')->row();
        $inventoryStocks= $this->db_readonly->select("sum(ifnull(current_quantity, 0)) as count")->get('procurement_delivery_challan_items')->row();

        $thr= 0;
        if(!empty($threshold)) {
          foreach($threshold as $key => $val) {
            if($val->threshold_quantity >= $val->count) {
              $thr ++;
            }
          }
        }

        $return= array(
          'skus' => $skus->count,
          'threshold' => $thr,
          'vendors' => $vendors->count,
          'inventorySold' => intval($inventoryQuantities->count) - intval($inventoryStocks->count),
          'inventoryStocks' => $inventoryStocks->count,
        );
      } else {
        $threshold= $this->db_readonly->select("pii.threshold_quantity,  SUM(pInvItems.current_quantity) as count")
          ->from('procurement_delivery_challan_items pInvItems')
          ->join('procurement_delivery_challan_master pim', 'pim.id = pInvItems.invoice_master_id')
          ->join('procurement_itemmaster_items pii', 'pii.id = pInvItems.proc_im_items_id')
          ->where('pim.sales_year_id', $activeSalesYear)
          ->group_by('pii.id')
          ->get()->result();

          $skus= $this->db_readonly->select("count(distinct(pii.id)) as count")
            ->from('procurement_delivery_challan_items pInvItems')
            ->join('procurement_delivery_challan_master pim', 'pim.id = pInvItems.invoice_master_id')
            ->join('procurement_itemmaster_items pii', 'pii.id = pInvItems.proc_im_items_id')
            ->where('pim.sales_year_id', $activeSalesYear)
            ->get()->row();

          $vendors= $this->db_readonly->select("count(distinct(pim.id)) as count")
            ->from('procurement_vendor_master pim')
            ->where("vendor_name != 'Initial Quantity'")
            ->where("status", 1)
            ->get()->row();

          $inventoryQuantities= $this->db_readonly->select("sum(ifnull(initial_quantity, 0)) as count")
            ->from('procurement_delivery_challan_items pInvItems')
            ->join('procurement_delivery_challan_master pim', 'pim.id = pInvItems.invoice_master_id')
            ->where('pim.sales_year_id', $activeSalesYear)
            ->get()->row();

          $inventoryStocks= $this->db_readonly->select("sum(ifnull(current_quantity, 0)) as count")
            ->from('procurement_delivery_challan_items pInvItems')
            ->join('procurement_delivery_challan_master pim', 'pim.id = pInvItems.invoice_master_id')
            ->where('pim.sales_year_id', $activeSalesYear)
            ->get()->row();

            $thr= 0;
            if(!empty($threshold)) {
              foreach($threshold as $key => $val) {
                if($val->threshold_quantity >= $val->count) {
                  $thr ++;
                }
              }
            }

            $return= array(
              'skus' => $skus->count,
              'threshold' => $thr,
              'vendors' => $vendors->count,
              'inventorySold' => intval($inventoryQuantities->count) - intval($inventoryStocks->count),
              'inventoryStocks' => $inventoryStocks->count,
            );
      }

      $this->db_readonly->select("sum(pii.current_quantity) as current_quantity, sum( pii.current_quantity * ( ifnull(pii.price, 0) + ifnull(pii.price, 0)*(ifnull(pii.cgst, 0) / 100) +  ifnull(pii.price, 0)*(ifnull(pii.sgst, 0) / 100) )) as purchase_cost")
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
            ->join('procurement_itemmaster_category pic', 'pic.id = pii.proc_im_category_id')
            ->join('procurement_itemmaster_subcategory pis', 'pis.id = pii.proc_im_subcategory_id');
        if($activeSalesYear != 0) {
          $this->db_readonly->where('pim.sales_year_id', $activeSalesYear);
        }
        $cats= $this->db_readonly->where('pii.is_closed', 1) // Which is not closed
            ->get()->row();

        if(!empty($cats)) {
          $return['purchase_cost']= number_format($cats->purchase_cost, 0);
          $return['current_quantity']= $cats->current_quantity;
        }

      return $return;
    }

    public function get_sales_year() {
      return $this->db_readonly->get('procurement_sales_year')->result();
    }

    public function getInterviewerId($id){
      $this->db_readonly->select('count(staff_id) as count_id ');
      $this->db_readonly->from('staff_recruitment_candidtes_interviews_interviewers');
      $this->db_readonly->where('staff_id',$id);
      $count_id=$this->db_readonly->get()->row()->count_id;
      if($count_id>0){
        return true;
      }else{
        return false;
      }

    }

    public function getAllTasksBoardWise($board_id){
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $result = $this->db_readonly->select('smt.id as id')
                                ->from('stb_mytasklists smt')
                                ->where('smt.board_id', $board_id)
                                ->where('smt.created_by', $staff_id)
                                ->where('smt.is_deleted', 0)
                                ->get()
                                ->result();
      if(!empty($result)){
        $task_details = $this->getTaskDetailsBoardWise($result, $staff_id);
        return $task_details;
      }else{
        return false;
      }
    }

    public function getTaskDetailsBoardWise($result, $staff_id){
      $idArray = array_map(function($item) {
          return (string) $item->id;
      }, $result);
      $tasks = $this->db_readonly->select('id, task_name, priority, delegated_to_task_id, due_date')
                                ->from('stb_mytasks smt')
                                ->where_in('smt.stb_mytasklist_id', $idArray)
                                ->where('smt.created_by', $staff_id)
                                ->where('smt.is_deleted', 0)
                                ->where('smt.status', 1)
                                ->get()
                                ->result();

      if(empty($tasks)){
        return false;
      }else{
        $open_tasks = 0;
        $delegated_tasks = 0;
        $high_priority_tasks = 0;
        $due_today_tasks = 0;
        $high_priority_due_today = 0;
        $overdue_tasks = 0;

        $today = date('Y-m-d');

        foreach ($tasks as $task) {
            if (empty($task->delegated_to_task_id)) {
                $open_tasks++;
            }

            if (!empty($task->delegated_to_task_id)) {
                $delegated_tasks++;
            }

            if ($task->priority == 1 && empty($task->delegated_to_task_id)) {
                $high_priority_tasks++;
            }

            if (empty($task->delegated_to_task_id) && date('Y-m-d', strtotime($task->due_date)) == $today) {
                $due_today_tasks++;
            }

            if ($task->priority == 1 && empty($task->delegated_to_task_id) && date('Y-m-d', strtotime($task->due_date)) == $today) {
                $high_priority_due_today++;
            }

            if (!empty($task->due_date) && empty($task->delegated_to_task_id) && date('Y-m-d', strtotime($task->due_date)) < $today) {
                $overdue_tasks++;
            }
        }

        $result = [
          'total_open_tasks' => $open_tasks,
          'total_delegated_tasks' => $delegated_tasks,
          'total_high_priority_tasks' => $high_priority_tasks,
          'total_due_today_tasks' => $due_today_tasks,
          'total_high_priority_due_today' => $high_priority_due_today,
          'total_overdue_tasks' => $overdue_tasks,
        ];

        return $result;
      }
    }

    public function categoryStockDetails($activeSalesYear, $force_refresh = false) {
      // Cache copied from Shrawan's code
      $this->load->driver('cache', array('adapter' => 'file'));
      $school_sub_domain = CONFIG_ENV['school_sub_domain'];

      if (!empty($school_sub_domain)) {
        $cache_key = $school_sub_domain . "_category_stock_details_widget";

        // Check if the result is already cached
        $result = $this->cache->get($cache_key);
        if ($force_refresh || !$result || empty($result)) {
            // Cache miss, so query the database
            log_message('error', 'Creating cache ' . $cache_key);
            $result = $this->category_stock_details_from_query($activeSalesYear);
            // Save the result to cache
            $half_day = 6 * 3600;
            $this->cache->save($cache_key, $result, $half_day); //half day cache time
        } else {
            // Cache hit, use the cached result
            log_message('error', 'cache hit! ' . $cache_key);
            $result = $this->cache->get($cache_key);
            // echo '<pre>'; print_r($result); die();
        }
      } else {
          //Do not work with cache if $school_sub_domain is not defined
          $result =$this->category_stock_details_from_query($activeSalesYear);
      }

      return $result;
    }

    function category_stock_details_from_query($activeSalesYear) {
        $this->db_readonly->select("pic.category_name, pis.subcategory_name, sum(pii.current_quantity) as current_quantity, sum( pii.current_quantity * ( ifnull(pii.price, 0) + ifnull(pii.price, 0)*(ifnull(pii.cgst, 0) / 100) +  ifnull(pii.price, 0)*(ifnull(pii.sgst, 0) / 100) )) as purchase_cost")
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
            ->join('procurement_itemmaster_category pic', 'pic.id = pii.proc_im_category_id')
            ->join('procurement_itemmaster_subcategory pis', 'pis.id = pii.proc_im_subcategory_id');
        if($activeSalesYear != 0) {
          $this->db_readonly->where('pim.sales_year_id', $activeSalesYear);
        }
        $cats= $this->db_readonly->where('pii.is_closed', 1) // Which is not closed
            ->group_by('pii.proc_im_subcategory_id')
            ->order_by('pii.proc_im_category_id, pii.proc_im_subcategory_id')
            ->get()->result();

      $quats= 0;
      $vals= 0;
      if(!empty($cats)) {
        foreach($cats as $key => $val) {
          $quats += $val->current_quantity;
          $vals += $val->purchase_cost;
        }
      }
      $result= ['cats' => $cats, 'quats' => $quats, 'vals' => $vals];

      return $result;
  }

  public function get_staff_locked_unlocked_profile_status($staff_id){

    $this->db_readonly->select('profile_status');
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('id',$staff_id);

    return $this->db_readonly->get()->row()->profile_status;
    
  }

public function get_att_v2_data($studentData, $studentId, $acadYearId) {
    // Initialize result array
    $attendanceStats = [
        'present_days' => 0,
        'absence_days' => 0,
        'late_days' => 0,
        'total_days' => 0,
        'academic_year' => ''
    ];

    // 1. Get academic year date range and holidays
    $this->db_readonly->select('master.academic_year, master.id, master.calendar_name, master.start_date, master.end_date, events.event_name, events.from_date, events.to_date');
    $this->db_readonly->from('calendar_v2_master master');
    $this->db_readonly->join('calendar_events_v2 events', 'master.id = events.calendar_v2_master_id', 'left');
    $this->db_readonly->where('master.academic_year', $acadYearId); // Changed from $this->yearId to $acadYearId
    $this->db_readonly->where('master.target_group', 'students');
    $academicYearData = $this->db_readonly->get()->result();
    if (empty($academicYearData)) {
        return $attendanceStats;
    }

    // Get academic year info from first row
    $academicYear = $academicYearData[0];
    $attendanceStats['academic_year'] = $this->acad_year->getAcadYear($acadYearId);

    // Prepare holidays array
    $holidays = [];
    foreach ($academicYearData as $row) {
        if ($row->event_name === 'holiday' || $row->event_name === 'holiday_range') {
            if ($row->event_name === 'holiday') {
                $holidays[] = $row->from_date;
            } else {
                // For holiday ranges, add all dates in the range
                $current = strtotime($row->from_date);
                $end = strtotime($row->to_date);
                while ($current <= $end) {
                    $holidays[] = date('Y-m-d', $current);
                    $current = strtotime('+1 day', $current);
                }
            }
        }
    }
    $holidays = array_unique($holidays);

    // 2. Get all attendance records for the student
    $this->db_readonly->select('
        session.att_taken_date,
        student.morning_session_status,
        student.afternoon_session_status,
        IFNULL(student.is_late, 0) as is_late
    ');
    $this->db_readonly->from('student_admission admission');
    $this->db_readonly->join('attendance_std_day_v2_students student', 'admission.id = student.student_admission_id');
    $this->db_readonly->join('attendance_std_day_v2_session session', 'session.id = student.attendance_day_v2_session_id');
    $this->db_readonly->where('admission.id', $studentId);
    $this->db_readonly->where('session.att_taken_date >=', $academicYear->start_date);
    $this->db_readonly->where('session.att_taken_date <=', $academicYear->end_date);
    $attendanceRecords = $this->db_readonly->get()->result();

    // Get session_count day wise
    $sesCountObj = $this->db_readonly->select("day_name, session_count")
        ->where('calendar_v2_master_id', $academicYear->id)
        ->get('calendar_events_v2_override_sessions')->result();

    $record_session_count_obj = new stdClass();
    if (!empty($sesCountObj)) {
        foreach ($sesCountObj as $val) {
            $day = $val->day_name;
            $record_session_count_obj->$day = $val->session_count;
        }
    }

    // 3. Process all records in memory
    foreach ($attendanceRecords as $record) {
        // Skip if date is a holiday
        if (in_array($record->att_taken_date, $holidays)) {
            continue;
        }

        // Checking day name and session count
        $current_day_name = date("l", strtotime($record->att_taken_date));
        $record_session_count = isset($record_session_count_obj->$current_day_name) ? $record_session_count_obj->$current_day_name : 1;
        
        if ($record_session_count == 0) {
            continue;
        }

        // Count late days
        if ($record->is_late == 1) {
            $attendanceStats['late_days']++;
        }

        // Calculate presence based on session count
        if ($record_session_count == 1) {
            // Single session day - full day counts
            if ($record->morning_session_status == 1) {
                $attendanceStats['present_days'] += 1;
            } else {
                $attendanceStats['absence_days'] += 1;
            }
        } elseif ($record_session_count == 2) {
            // Two session day - half day counts
            if ($record->morning_session_status == 1 && $record->afternoon_session_status == 1) {
                $attendanceStats['present_days'] += 1;
            } elseif ($record->morning_session_status == 0 && $record->afternoon_session_status == 0) {
                $attendanceStats['absence_days'] += 1;
            } else {
                $attendanceStats['present_days'] += 0.5;
                $attendanceStats['absence_days'] += 0.5;
            }
        }
    }
// echo "<pre>"; print_r($attendanceStats);
    return $attendanceStats;
}
}
 